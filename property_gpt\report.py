"""
Report Generator for PropertyGPT.

This module generates comprehensive bug reports in Rich Markdown format including:
1. Violated properties with concrete exploit transactions/models
2. Verified invariants that passed all checks
3. Optional failing fuzz pool results
4. Summary statistics and recommendations
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.markdown import Markdown
from rich.table import Table

from .utils import (
    InvariantProperty,
    format_duration,
    save_json_safe,
)


class ReportGenerator:
    """Generates comprehensive analysis reports for PropertyGPT."""
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize the report generator.
        
        Args:
            console: Rich console for output (optional)
        """
        self.console = console or Console()
    
    def generate_report(
        self,
        contract_path: Path,
        invariants: List[InvariantProperty],
        output_path: Path = Path("report.md"),
        pools_data: Optional[Dict[str, Any]] = None,
        analysis_time: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Generate a comprehensive analysis report.
        
        Args:
            contract_path: Path to the analyzed contract
            invariants: List of analyzed invariants
            output_path: Output path for the report
            pools_data: Optional fuzz pool data
            analysis_time: Total analysis time in seconds
            metadata: Additional metadata
            
        Returns:
            True if report was generated successfully
        """
        try:
            # Categorize invariants
            violated = [inv for inv in invariants if inv.proof_status == "violated"]
            verified = [inv for inv in invariants if inv.proof_status == "verified"]
            timeouts = [inv for inv in invariants if inv.proof_status == "timeout"]
            unknown = [inv for inv in invariants if inv.proof_status == "unknown"]
            
            # Generate report content
            report_content = self._generate_markdown_report(
                contract_path=contract_path,
                violated=violated,
                verified=verified,
                timeouts=timeouts,
                unknown=unknown,
                pools_data=pools_data,
                analysis_time=analysis_time,
                metadata=metadata or {}
            )
            
            # Write report to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            # Display summary to console
            self._display_summary(violated, verified, timeouts, unknown, analysis_time)
            
            # Save JSON summary
            json_path = output_path.with_suffix('.json')
            self._save_json_summary(
                json_path, contract_path, invariants, analysis_time, metadata
            )
            
            logging.info(f"Report generated: {output_path}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to generate report: {e}")
            return False
    
    def _generate_markdown_report(
        self,
        contract_path: Path,
        violated: List[InvariantProperty],
        verified: List[InvariantProperty],
        timeouts: List[InvariantProperty],
        unknown: List[InvariantProperty],
        pools_data: Optional[Dict[str, Any]],
        analysis_time: float,
        metadata: Dict[str, Any]
    ) -> str:
        """Generate the main markdown report content."""
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""# PropertyGPT Analysis Report

**Contract:** `{contract_path.name}`  
**Analysis Date:** {timestamp}  
**Analysis Time:** {format_duration(analysis_time)}  
**Total Invariants:** {len(violated) + len(verified) + len(timeouts) + len(unknown)}

## 📊 Summary

| Status | Count | Description |
|--------|-------|-------------|
| ❌ Violated | {len(violated)} | Properties with counterexamples found |
| ✅ Verified | {len(verified)} | Properties proven to hold |
| ⏱️ Timeout | {len(timeouts)} | Properties that timed out |
| ❓ Unknown | {len(unknown)} | Properties with inconclusive results |

"""
        
        # Add violated properties section
        if violated:
            report += self._generate_violated_section(violated)
        
        # Add verified properties section
        if verified:
            report += self._generate_verified_section(verified)
        
        # Add timeout/unknown sections
        if timeouts:
            report += self._generate_timeout_section(timeouts)
        
        if unknown:
            report += self._generate_unknown_section(unknown)
        
        # Add fuzz pool results if available
        if pools_data:
            report += self._generate_pools_section(pools_data)
        
        # Add recommendations
        report += self._generate_recommendations(violated, verified)
        
        # Add metadata
        if metadata:
            report += self._generate_metadata_section(metadata)
        
        return report
    
    def _generate_violated_section(self, violated: List[InvariantProperty]) -> str:
        """Generate the violated properties section."""
        
        section = f"""
## ❌ Violated Properties

Found {len(violated)} property violation(s) that indicate potential bugs:

"""
        
        for i, inv in enumerate(violated, 1):
            section += f"""
### {i}. {inv.description}

**Property ID:** `{inv.id}`  
**Function:** `{inv.function_name}`  
**Type:** {inv.property_type}  
**Confidence Score:** {inv.confidence_score:.2f}

**Invariant Code:**
```solidity
{inv.code}
```

"""
            
            if inv.counter_example:
                section += "**Counterexample:**\n"
                section += "```json\n"
                section += json.dumps(inv.counter_example, indent=2)
                section += "\n```\n"
                
                # Generate exploit scenario if possible
                exploit = self._generate_exploit_scenario(inv)
                if exploit:
                    section += f"\n**Potential Exploit:**\n{exploit}\n"
            
            section += "\n---\n"
        
        return section
    
    def _generate_verified_section(self, verified: List[InvariantProperty]) -> str:
        """Generate the verified properties section."""
        
        section = f"""
## ✅ Verified Properties

Successfully verified {len(verified)} property(ies):

"""
        
        for i, inv in enumerate(verified, 1):
            section += f"""
### {i}. {inv.description}

**Property ID:** `{inv.id}`  
**Function:** `{inv.function_name}`  
**Type:** {inv.property_type}  
**Confidence Score:** {inv.confidence_score:.2f}

**Invariant Code:**
```solidity
{inv.code}
```

✅ **Status:** This property holds for all possible inputs and states.

"""
        
        return section
    
    def _generate_timeout_section(self, timeouts: List[InvariantProperty]) -> str:
        """Generate the timeout properties section."""
        
        section = f"""
## ⏱️ Timeout Properties

{len(timeouts)} property(ies) timed out during analysis:

"""
        
        for inv in timeouts:
            section += f"- **{inv.id}:** {inv.description} (Function: `{inv.function_name}`)\n"
        
        section += "\n*These properties may require longer analysis time or different solver settings.*\n\n"
        
        return section
    
    def _generate_unknown_section(self, unknown: List[InvariantProperty]) -> str:
        """Generate the unknown status properties section."""
        
        section = f"""
## ❓ Unknown Status Properties

{len(unknown)} property(ies) had inconclusive results:

"""
        
        for inv in unknown:
            section += f"- **{inv.id}:** {inv.description} (Function: `{inv.function_name}`)\n"
        
        section += "\n*These properties may have compilation issues or solver errors.*\n\n"
        
        return section
    
    def _generate_pools_section(self, pools_data: Dict[str, Any]) -> str:
        """Generate the fuzz pools section."""
        
        section = """
## ⚠️ Fuzz Pool Results

Additional testing results from fuzzing pools:

"""
        
        failing_pools = pools_data.get('failing_pools', [])
        if failing_pools:
            section += f"**Failing Pools:** {len(failing_pools)}\n\n"
            
            for pool in failing_pools:
                section += f"- **{pool.get('name', 'Unknown')}:** {pool.get('description', 'No description')}\n"
                if pool.get('failure_count'):
                    section += f"  - Failures: {pool['failure_count']}\n"
                if pool.get('error_message'):
                    section += f"  - Error: `{pool['error_message']}`\n"
        else:
            section += "✅ All fuzz pools passed.\n"
        
        section += "\n"
        return section
    
    def _generate_recommendations(
        self,
        violated: List[InvariantProperty],
        verified: List[InvariantProperty]
    ) -> str:
        """Generate recommendations based on analysis results."""
        
        section = """
## 🔧 Recommendations

"""
        
        if violated:
            section += f"""
### Critical Issues ({len(violated)} found)

The analysis found {len(violated)} property violation(s) that indicate potential security vulnerabilities:

"""
            
            # Group by function for recommendations
            functions_with_issues = set(inv.function_name for inv in violated)
            
            for func_name in functions_with_issues:
                func_violations = [inv for inv in violated if inv.function_name == func_name]
                section += f"- **Function `{func_name}`:** {len(func_violations)} violation(s)\n"
                
                # Specific recommendations based on property types
                violation_types = set(inv.property_type for inv in func_violations)
                if "precondition" in violation_types:
                    section += "  - Add input validation and bounds checking\n"
                if "postcondition" in violation_types:
                    section += "  - Review state changes and return value handling\n"
                if "cross_invariant" in violation_types:
                    section += "  - Check global state consistency\n"
            
            section += "\n**Immediate Actions:**\n"
            section += "1. Review and fix the violated properties\n"
            section += "2. Add comprehensive unit tests for edge cases\n"
            section += "3. Consider formal verification for critical functions\n"
            section += "4. Implement additional access controls if needed\n\n"
        
        if verified:
            section += f"""
### Verified Properties ({len(verified)} found)

✅ {len(verified)} properties were successfully verified, indicating good security practices in these areas.

"""
        
        section += """
### General Recommendations

1. **Regular Analysis:** Run PropertyGPT analysis on code changes
2. **Comprehensive Testing:** Combine formal verification with fuzzing
3. **Code Review:** Have security experts review critical functions
4. **Documentation:** Document all invariants and assumptions
5. **Monitoring:** Implement runtime checks for critical properties

"""
        
        return section
    
    def _generate_metadata_section(self, metadata: Dict[str, Any]) -> str:
        """Generate metadata section."""
        
        section = """
## 📋 Analysis Metadata

"""
        
        for key, value in metadata.items():
            section += f"**{key.replace('_', ' ').title()}:** {value}  \n"
        
        section += f"\n*Report generated by PropertyGPT v0.1.0*\n"
        
        return section
    
    def _generate_exploit_scenario(self, invariant: InvariantProperty) -> Optional[str]:
        """Generate a potential exploit scenario from counterexample."""
        
        if not invariant.counter_example:
            return None
        
        scenario = f"""
This violation suggests a potential exploit scenario:

1. **Target Function:** `{invariant.function_name}`
2. **Violation Type:** {invariant.property_type}
3. **Attack Vector:** Based on the counterexample values
"""
        
        # Add specific attack details based on counterexample
        if invariant.counter_example:
            scenario += "4. **Exploit Parameters:**\n"
            for key, value in invariant.counter_example.items():
                scenario += f"   - `{key}`: {value}\n"
        
        return scenario
    
    def _display_summary(
        self,
        violated: List[InvariantProperty],
        verified: List[InvariantProperty],
        timeouts: List[InvariantProperty],
        unknown: List[InvariantProperty],
        analysis_time: float
    ) -> None:
        """Display summary table to console."""
        
        table = Table(title="PropertyGPT Analysis Summary")
        table.add_column("Status", style="bold")
        table.add_column("Count", justify="right")
        table.add_column("Description")
        
        table.add_row("❌ Violated", str(len(violated)), "Properties with counterexamples")
        table.add_row("✅ Verified", str(len(verified)), "Properties proven to hold")
        table.add_row("⏱️ Timeout", str(len(timeouts)), "Properties that timed out")
        table.add_row("❓ Unknown", str(len(unknown)), "Properties with inconclusive results")
        
        self.console.print(table)
        self.console.print(f"\n⏱️ Analysis completed in {format_duration(analysis_time)}")
        
        if violated:
            self.console.print(f"\n[bold red]⚠️  Found {len(violated)} potential security issue(s)![/bold red]")
        else:
            self.console.print(f"\n[bold green]✅ No security violations found![/bold green]")
    
    def _save_json_summary(
        self,
        json_path: Path,
        contract_path: Path,
        invariants: List[InvariantProperty],
        analysis_time: float,
        metadata: Optional[Dict[str, Any]]
    ) -> None:
        """Save JSON summary of analysis results."""
        
        summary = {
            "contract_path": str(contract_path),
            "analysis_timestamp": datetime.now().isoformat(),
            "analysis_time_seconds": analysis_time,
            "total_invariants": len(invariants),
            "results": {
                "violated": len([inv for inv in invariants if inv.proof_status == "violated"]),
                "verified": len([inv for inv in invariants if inv.proof_status == "verified"]),
                "timeout": len([inv for inv in invariants if inv.proof_status == "timeout"]),
                "unknown": len([inv for inv in invariants if inv.proof_status == "unknown"])
            },
            "invariants": [
                {
                    "id": inv.id,
                    "description": inv.description,
                    "function_name": inv.function_name,
                    "property_type": inv.property_type,
                    "proof_status": inv.proof_status,
                    "confidence_score": inv.confidence_score,
                    "counter_example": inv.counter_example
                }
                for inv in invariants
            ],
            "metadata": metadata or {}
        }
        
        save_json_safe(summary, json_path)
