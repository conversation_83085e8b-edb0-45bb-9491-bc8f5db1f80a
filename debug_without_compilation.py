#!/usr/bin/env python3
"""
Debug PropertyGPT by generating invariants without compilation step.
"""

import os
from pathlib import Path
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def debug_generation_only():
    """Generate invariants without compilation to see what DeepSeek produces."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        print("🔍 Parsing Bank.sol functions...")
        
        # Parse functions from Bank.sol
        contract_path = Path("demo/Bank.sol")
        functions = generator._parse_contract_functions(contract_path)
        
        print(f"✅ Found {len(functions)} functions:")
        for func in functions:
            print(f"  - {func.name}({', '.join([p['name'] + ': ' + p['type'] for p in func.parameters])})")
        
        # Test with just one function to see the raw output
        if functions:
            test_function = functions[0]  # Take the first function
            print(f"\n🧠 Generating invariants for: {test_function.name}")
            
            # Generate invariants without compilation
            raw_invariants = generator._generate_function_invariants(test_function, [], k_similar=3, max_retries=1)
            
            print(f"\n📊 Generated {len(raw_invariants)} raw invariants:")
            for i, inv in enumerate(raw_invariants):
                print(f"\n--- Invariant {i+1} ---")
                print(f"ID: {inv.id}")
                print(f"Type: {inv.property_type}")
                print(f"Function: {inv.function_name}")
                print(f"Code: {inv.code}")
                print(f"Description: {inv.description}")
                print(f"Confidence: {inv.confidence_score}")
            
            # Now test compilation on just one invariant
            if raw_invariants:
                print(f"\n🔧 Testing compilation on first invariant...")
                test_inv = raw_invariants[0]
                
                # Try to compile it
                compiled_inv = generator._compile_and_fix_invariant(test_inv, contract_path, max_retries=1)
                
                if compiled_inv:
                    print(f"✅ Compilation status: {compiled_inv.compilation_status}")
                    if compiled_inv.compilation_status == "failed":
                        print(f"❌ Compilation error: {compiled_inv.compilation_error}")
                    else:
                        print(f"✅ Compiled successfully!")
                        print(f"Final code: {compiled_inv.code}")
                else:
                    print("❌ Compilation returned None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Debug PropertyGPT Generation (No Compilation)")
    print("=" * 60)
    
    success = debug_generation_only()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 Debug completed - check the output above")
    else:
        print("❌ Debug failed")
