#!/usr/bin/env python3
"""
Test manual compilation of a known invariant.
"""

from pathlib import Path
from property_gpt.utils import InvariantProperty
from property_gpt.gen_props import InvariantGenerator

def test_manual_compilation():
    """Test compilation with a manually created invariant."""
    
    print("🧪 Testing manual compilation...")
    
    # Create a generator (no client needed for this test)
    generator = InvariantGenerator(None, None, "test", 0.2)
    
    # Create a simple test invariant that should work
    test_invariant = InvariantProperty(
        id="test_deposit_pre_0",
        property_type="precondition",
        function_name="deposit",
        code="require(msg.value > 0, \"Deposit amount must be positive\");",
        description="Deposit amount must be positive",
        confidence_score=0.9
    )
    
    print(f"🔍 Testing invariant: {test_invariant.code}")
    
    # Test contract creation
    contract_code = generator._create_test_contract(test_invariant, Path("demo/Bank.sol"))
    
    # Save for inspection
    with open("test_manual_contract.sol", "w") as f:
        f.write(contract_code)
    
    print("✅ Contract saved to test_manual_contract.sol")
    print(f"📄 Contract preview:")
    print("-" * 50)
    print(contract_code)
    print("-" * 50)
    
    # Test compilation with our fixed method
    compiled_inv = generator._compile_and_fix_invariant(test_invariant, Path("demo/Bank.sol"), max_retries=1)
    
    if compiled_inv:
        print(f"✅ Compilation result received")
        print(f"📊 Status: {compiled_inv.compilation_status}")
        if compiled_inv.compilation_status == "failed":
            print(f"❌ Error: {getattr(compiled_inv, 'compilation_error', 'No error details')}")
        else:
            print(f"✅ Compilation succeeded!")
    else:
        print("❌ Compilation returned None (this shouldn't happen with our fix)")
    
    # Also test manual solc compilation
    print(f"\n🔧 Testing manual solc compilation...")
    from property_gpt.utils import run_command
    cmd = ["solc", "test_manual_contract.sol"]
    returncode, stdout, stderr = run_command(cmd)
    
    print(f"Return code: {returncode}")
    print(f"Stdout: {stdout}")
    print(f"Stderr: {stderr}")
    
    if returncode == 0:
        print("✅ Manual solc compilation succeeded!")
        return True
    else:
        print(f"❌ Manual solc compilation failed")
        return False

if __name__ == "__main__":
    print("🚀 Testing Manual Compilation")
    print("=" * 50)
    
    success = test_manual_compilation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Manual compilation works!")
    else:
        print("⚠️ Manual compilation failed - need to investigate further")
