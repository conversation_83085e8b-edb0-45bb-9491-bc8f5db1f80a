{"contract_path": "demo\\Bank.sol", "analysis_timestamp": "2025-07-10T14:14:20.653742", "analysis_time_seconds": 250.*************, "total_invariants": 3, "results": {"violated": 0, "verified": 3, "timeout": 0, "unknown": 0}, "invariants": [{"id": "getBalance_post_2", "description": "Postcondition 3 for getBalance", "function_name": "getBalance", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.243, "counter_example": null}, {"id": "emergencyWithdraw_post_4", "description": "Postcondition 5 for emergencyWithdraw", "function_name": "emergencyWithdraw", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.237, "counter_example": null}, {"id": "getContractBalance_post_4", "description": "Postcondition 5 for getContractBalance", "function_name": "getContractBalance", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.237, "counter_example": null}], "metadata": {"model": "deepseek-chat", "api_provider": "deepseek", "solver": "z3", "k_similar": 3, "temperature": 0.2, "timeout": 30, "kb_available": false}}