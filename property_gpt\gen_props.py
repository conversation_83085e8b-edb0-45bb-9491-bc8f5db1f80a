"""
Invariant Generator for PropertyGPT.

This module generates invariants for smart contracts by:
1. Parsing contracts with <PERSON><PERSON><PERSON> to extract function information
2. Retrieving similar properties from the knowledge base using RAG
3. Prompting GPT-4o to generate new invariants
4. Running a compile-fix loop to ensure invariants compile
5. Ranking invariants using NDSS-25 weighted scoring
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from openai import OpenAI
from rich.progress import Progress, SpinnerColumn, TextColumn

from .kb import KnowledgeBaseSearcher
from .utils import (
    PropertyGPTError,
    CompilationError,
    InvariantProperty,
    ContractFunction,
    SimilarProperty,
    run_command,
    create_temp_file,
    parse_solidity_errors,
    calculate_edit_distance,
    calculate_keyword_coverage,
    calculate_complexity_score,
)


class InvariantGenerator:
    """Generates invariants for smart contracts using AI and RAG."""

    def __init__(
        self,
        openai_client: OpenAI,
        kb_searcher: Optional[KnowledgeBaseSearcher] = None,
        model: str = "gpt-4o",
        temperature: float = 0.2
    ):
        """
        Initialize the invariant generator.

        Args:
            openai_client: OpenAI-compatible client instance
            kb_searcher: Knowledge base searcher for RAG
            model: Model to use (gpt-4o, deepseek-chat, etc.)
            temperature: Generation temperature
        """
        self.client = openai_client
        self.kb_searcher = kb_searcher
        self.model = model
        self.temperature = temperature
    
    def generate_invariants(
        self,
        contract_path: Path,
        k_similar: int = 3,
        max_retries: int = 2,
        top_n: int = 3
    ) -> List[InvariantProperty]:
        """
        Generate invariants for a smart contract.
        
        Args:
            contract_path: Path to the contract file
            k_similar: Number of similar properties to retrieve
            max_retries: Maximum compilation fix retries
            top_n: Number of top-ranked invariants to return
            
        Returns:
            List of generated and ranked invariants
        """
        logging.info(f"Generating invariants for {contract_path}")
        
        # Parse contract with Slither
        functions = self._parse_contract_functions(contract_path)
        if not functions:
            raise PropertyGPTError(f"No functions found in contract {contract_path}")
        
        logging.info(f"Found {len(functions)} functions in contract")
        
        # Generate invariants for each function
        all_invariants = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
        ) as progress:
            
            task = progress.add_task("Generating invariants...", total=len(functions))
            
            for func in functions:
                func_invariants = self._generate_function_invariants(
                    func, contract_path, k_similar, max_retries
                )
                all_invariants.extend(func_invariants)
                progress.advance(task)
        
        # Rank and filter invariants
        ranked_invariants = self._rank_invariants(all_invariants, functions)
        
        logging.info(f"Generated {len(all_invariants)} invariants, returning top {top_n}")
        return ranked_invariants[:top_n]
    
    def _parse_contract_functions(self, contract_path: Path) -> List[ContractFunction]:
        """
        Parse contract functions using Slither.
        
        Args:
            contract_path: Path to contract file
            
        Returns:
            List of contract functions
        """
        try:
            # Run slither to get function information
            cmd = ["slither", str(contract_path), "--print", "function-summary", "--json", "-"]
            returncode, stdout, stderr = run_command(cmd)
            
            if returncode != 0:
                logging.warning(f"Slither analysis failed: {stderr}")
                return self._fallback_parse_functions(contract_path)
            
            # Parse Slither output
            try:
                slither_output = json.loads(stdout)
                return self._extract_functions_from_slither(slither_output)
            except json.JSONDecodeError:
                logging.warning("Failed to parse Slither JSON output")
                return self._fallback_parse_functions(contract_path)
                
        except Exception as e:
            logging.warning(f"Slither parsing failed: {e}")
            return self._fallback_parse_functions(contract_path)
    
    def _extract_functions_from_slither(self, slither_output: Dict) -> List[ContractFunction]:
        """Extract function information from Slither output."""
        functions = []

        # Check if this is a printer output (function-summary)
        printers = slither_output.get("results", {}).get("printers", [])
        if printers:
            # Extract from function-summary printer output
            for printer in printers:
                if printer.get("printer") == "function-summary":
                    # Parse the description text to extract function info
                    description = printer.get("description", "")
                    functions.extend(self._parse_function_summary_text(description))
                    break

        # Fallback: check detectors format (for other Slither outputs)
        for result in slither_output.get("results", {}).get("detectors", []):
            for element in result.get("elements", []):
                if element.get("type") == "function":
                    func = ContractFunction(
                        name=element.get("name", "unknown"),
                        signature=element.get("signature", ""),
                        visibility=element.get("visibility", "public"),
                        state_mutability=element.get("state_mutability", "nonpayable"),
                        parameters=element.get("parameters", []),
                        returns=element.get("returns", [])
                    )

                    # Only include public/external functions
                    if func.visibility in ["public", "external"]:
                        functions.append(func)

        return functions

    def _parse_function_summary_text(self, description: str) -> List[ContractFunction]:
        """Parse function information from Slither function-summary text output."""
        functions = []

        # Look for function table in the description
        lines = description.split('\n')
        in_function_table = False

        for line in lines:
            # Skip ANSI color codes and table formatting
            clean_line = re.sub(r'\x1b\[[0-9;]*m', '', line)

            # Check if we're in the function table (look for header)
            if '| Function' in clean_line and '| Visibility' in clean_line:
                in_function_table = True
                continue
            elif in_function_table and clean_line.strip().startswith('+') and '---' in clean_line:
                # Table separator or end
                continue
            elif in_function_table and '| Modifiers' in clean_line:
                # End of function table, start of modifiers table
                break
            elif in_function_table and '|' in clean_line and clean_line.strip() != '|':
                # Parse function row
                parts = [part.strip() for part in clean_line.split('|')]
                if len(parts) >= 3:
                    func_name = parts[1].strip()
                    visibility = parts[2].strip()

                    # Skip empty rows, continuation rows, and invalid entries
                    if (func_name and
                        func_name not in ['Function', '', ' '] and
                        not func_name.startswith('[') and  # Skip continuation lines
                        visibility in ['external', 'public']):

                        # Extract function name and parameters
                        if '(' in func_name:
                            # Parse function signature
                            func_signature = func_name
                            func_name_only = func_name.split('(')[0]
                        else:
                            func_signature = f"{func_name}()"
                            func_name_only = func_name

                        # Skip constructor and receive functions for invariant generation
                        if func_name_only not in ['constructor', 'receive']:
                            func = ContractFunction(
                                name=func_name_only,
                                signature=func_signature,
                                visibility=visibility,
                                state_mutability="nonpayable",  # Default
                                parameters=[],
                                returns=[]
                            )
                            functions.append(func)

        return functions
    
    def _fallback_parse_functions(self, contract_path: Path) -> List[ContractFunction]:
        """Fallback function parsing using regex."""
        try:
            with open(contract_path, 'r') as f:
                content = f.read()
        except Exception:
            return []
        
        functions = []
        
        # Simple regex to find function declarations
        func_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*(public|external)?\s*(view|pure|payable|nonpayable)?'
        
        for match in re.finditer(func_pattern, content):
            func_name = match.group(1)
            visibility = match.group(2) or "public"
            state_mutability = match.group(3) or "nonpayable"
            
            if visibility in ["public", "external"]:
                func = ContractFunction(
                    name=func_name,
                    signature=match.group(0),
                    visibility=visibility,
                    state_mutability=state_mutability,
                    parameters=[],
                    returns=[]
                )
                functions.append(func)
        
        return functions
    
    def _generate_function_invariants(
        self,
        function: ContractFunction,
        contract_path: Path,
        k_similar: int,
        max_retries: int
    ) -> List[InvariantProperty]:
        """Generate invariants for a specific function."""
        
        # Retrieve similar properties if KB is available
        similar_props = []
        if self.kb_searcher:
            query = f"function {function.name} {function.signature}"
            search_results = self.kb_searcher.search(query, k_similar)
            
            for result in search_results:
                similar_props.append(SimilarProperty(
                    content=result.get('content', ''),
                    similarity_score=result.get('similarity_score', 0.0),
                    source_file=result.get('source_file', ''),
                    context=result.get('chunk_type', '')
                ))
        
        # Generate invariants using GPT
        prompt = self._create_generation_prompt(function, similar_props)
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature
            )
            
            generated_text = response.choices[0].message.content
            invariants = self._parse_generated_invariants(generated_text, function)
            
        except Exception as e:
            logging.error(f"Failed to generate invariants for {function.name}: {e}")
            return []
        
        # Compile and fix invariants
        compiled_invariants = []
        for invariant in invariants:
            fixed_invariant = self._compile_and_fix_invariant(
                invariant, contract_path, max_retries
            )
            if fixed_invariant:
                compiled_invariants.append(fixed_invariant)
        
        return compiled_invariants
    
    def _create_generation_prompt(
        self,
        function: ContractFunction,
        similar_props: List[SimilarProperty]
    ) -> str:
        """Create prompt for invariant generation."""
        
        prompt = f"""You are an expert in smart contract formal verification. Generate invariants for the following function:

Function: {function.name}
Signature: {function.signature}
Visibility: {function.visibility}
State Mutability: {function.state_mutability}

"""
        
        if similar_props:
            prompt += "Here are similar properties from existing specifications:\n\n"
            for i, prop in enumerate(similar_props, 1):
                prompt += f"Example {i} (similarity: {prop.similarity_score:.2f}):\n"
                prompt += f"{prop.content[:500]}...\n\n"
        
        prompt += """Please generate exactly 5 precondition/postcondition assertions and 2 cross-function invariants for this function.

Requirements:
1. Use Solidity syntax with require() for preconditions and assert() for postconditions
2. Focus on common vulnerability patterns: overflow/underflow, access control, state consistency
3. Make assertions specific and testable
4. Include meaningful error messages
5. Consider edge cases and boundary conditions

Format your response as:
PRECONDITIONS:
1. require(condition, "error message");
2. require(condition, "error message");
...

POSTCONDITIONS:
1. assert(condition);
2. assert(condition);
...

CROSS_INVARIANTS:
1. assert(global_condition);
2. assert(global_condition);

Generate practical, compilable assertions that catch real bugs."""
        
        return prompt
    
    def _parse_generated_invariants(
        self,
        generated_text: str,
        function: ContractFunction
    ) -> List[InvariantProperty]:
        """Parse generated invariants from GPT response."""
        
        invariants = []
        
        # Extract preconditions
        precond_pattern = r'PRECONDITIONS?:\s*(.*?)(?=POSTCONDITIONS?:|CROSS_INVARIANTS?:|$)'
        precond_match = re.search(precond_pattern, generated_text, re.DOTALL | re.IGNORECASE)
        
        if precond_match:
            precond_text = precond_match.group(1)
            preconditions = re.findall(r'require\([^;]+\);', precond_text)
            
            for i, precond in enumerate(preconditions):
                invariants.append(InvariantProperty(
                    id=f"{function.name}_pre_{i}",
                    description=f"Precondition {i+1} for {function.name}",
                    code=precond,
                    function_name=function.name,
                    property_type="precondition"
                ))
        
        # Extract postconditions
        postcond_pattern = r'POSTCONDITIONS?:\s*(.*?)(?=CROSS_INVARIANTS?:|$)'
        postcond_match = re.search(postcond_pattern, generated_text, re.DOTALL | re.IGNORECASE)
        
        if postcond_match:
            postcond_text = postcond_match.group(1)
            postconditions = re.findall(r'assert\([^;]+\);', postcond_text)
            
            for i, postcond in enumerate(postconditions):
                invariants.append(InvariantProperty(
                    id=f"{function.name}_post_{i}",
                    description=f"Postcondition {i+1} for {function.name}",
                    code=postcond,
                    function_name=function.name,
                    property_type="postcondition"
                ))
        
        # Extract cross-invariants
        cross_pattern = r'CROSS_INVARIANTS?:\s*(.*?)$'
        cross_match = re.search(cross_pattern, generated_text, re.DOTALL | re.IGNORECASE)
        
        if cross_match:
            cross_text = cross_match.group(1)
            cross_invariants = re.findall(r'assert\([^;]+\);', cross_text)
            
            for i, cross_inv in enumerate(cross_invariants):
                invariants.append(InvariantProperty(
                    id=f"{function.name}_cross_{i}",
                    description=f"Cross-invariant {i+1} for {function.name}",
                    code=cross_inv,
                    function_name=function.name,
                    property_type="cross_invariant"
                ))
        
        return invariants
    
    def _compile_and_fix_invariant(
        self,
        invariant: InvariantProperty,
        contract_path: Path,
        max_retries: int
    ) -> Optional[InvariantProperty]:
        """Compile invariant and attempt to fix compilation errors."""
        
        # Create test contract with invariant
        test_contract = self._create_test_contract(invariant, contract_path)
        
        for attempt in range(max_retries + 1):
            temp_file = create_temp_file(test_contract)
            
            try:
                # Try to compile (removed --formal-verification flag as it's not widely supported)
                cmd = ["solc", str(temp_file)]
                returncode, stdout, stderr = run_command(cmd)
                
                if returncode == 0:
                    invariant.compilation_status = "success"
                    return invariant
                
                # Parse compilation errors
                errors = parse_solidity_errors(stderr)
                if not errors or attempt >= max_retries:
                    break
                
                # Try to fix errors
                fixed_code = self._fix_compilation_errors(invariant.code, errors)
                if fixed_code != invariant.code:
                    invariant.code = fixed_code
                    test_contract = self._create_test_contract(invariant, contract_path)
                else:
                    break  # No fix possible
                    
            finally:
                temp_file.unlink(missing_ok=True)
        
        invariant.compilation_status = "failed"
        return None
    
    def _create_test_contract(self, invariant: InvariantProperty, contract_path: Path) -> str:
        """Create a test contract to compile the invariant."""

        # Read original contract
        try:
            with open(contract_path, 'r') as f:
                original_contract = f.read()
        except Exception:
            original_contract = "// Original contract could not be read"

        # Check if original contract already has SPDX license
        has_spdx = "SPDX-License-Identifier" in original_contract
        has_pragma = "pragma solidity" in original_contract

        # Build test contract without duplicating headers
        if has_spdx and has_pragma:
            # Original contract has both, just append test contract
            test_contract = f"""{original_contract}

contract TestInvariant {{
    function testInvariant() public pure {{
        {invariant.code}
    }}
}}
"""
        else:
            # Add missing headers
            test_contract = f"""// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

{original_contract}

contract TestInvariant {{
    function testInvariant() public pure {{
        {invariant.code}
    }}
}}
"""
        return test_contract
    
    def _fix_compilation_errors(self, code: str, errors: List[Dict]) -> str:
        """Attempt to fix simple compilation errors."""
        
        fixed_code = code
        
        for error in errors:
            message = error.get('message', '').lower()
            
            # Fix common issues
            if 'undeclared identifier' in message:
                # Try to add common variable declarations
                if 'msg.sender' in code and 'address' not in fixed_code:
                    fixed_code = f"address sender = msg.sender; {fixed_code}"
                if 'block.timestamp' in code and 'uint256' not in fixed_code:
                    fixed_code = f"uint256 timestamp = block.timestamp; {fixed_code}"
            
            elif 'type error' in message and 'uint256' in message:
                # Try to cast to uint256
                fixed_code = re.sub(r'\b(\w+)\s*([><=!]+)\s*(\d+)', r'uint256(\1) \2 \3', fixed_code)
        
        return fixed_code
    
    def _rank_invariants(
        self,
        invariants: List[InvariantProperty],
        functions: List[ContractFunction]
    ) -> List[InvariantProperty]:
        """Rank invariants using NDSS-25 weighted scoring."""
        
        # Extract keywords from function names for coverage calculation
        keywords = []
        for func in functions:
            keywords.extend(func.name.lower().split('_'))
        keywords = list(set(keywords))  # Remove duplicates
        
        for invariant in invariants:
            # Calculate individual scores
            edit_dist = calculate_edit_distance(invariant.code, "")  # Distance from empty
            keyword_cov = calculate_keyword_coverage(invariant.code, keywords)
            complexity = calculate_complexity_score(invariant.code)
            
            # NDSS-25 weighted score (lower is better for edit distance)
            # Normalize edit distance (invert so higher is better)
            norm_edit = max(0, 1.0 - (edit_dist / 100.0))
            
            # Weighted combination
            invariant.confidence_score = (
                0.3 * norm_edit +
                0.4 * keyword_cov +
                0.3 * min(complexity / 10.0, 1.0)
            )
        
        # Sort by confidence score (descending)
        return sorted(invariants, key=lambda x: x.confidence_score, reverse=True)
