# PropertyGPT Core Dependencies
# These are the runtime dependencies needed to run PropertyGPT

# CLI and UI
typer>=0.9.0
rich>=13.0.0

# AI and ML
openai>=1.0.0
faiss-cpu>=1.7.0
numpy>=1.24.0

# Smart Contract Analysis
slither-analyzer>=0.10.0

# SMT Solving
z3-solver>=4.12.0

# Utilities
requests>=2.28.0

# Note: Additional system dependencies required:
# - Foundry (foundryup): For Solidity compilation
# - Z3 or CVC5: SMT solvers (can be installed via package managers)
# - Node.js (optional): For some Slither features
#
# Install system dependencies:
# macOS: brew install z3
# Ubuntu: sudo apt-get install z3
# Foundry: curl -L https://foundry.paradigm.xyz | bash && foundryup
