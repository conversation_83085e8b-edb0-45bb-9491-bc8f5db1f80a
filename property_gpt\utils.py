"""
Utility functions and shared data structures for PropertyGPT.

This module provides common functionality used across the application including
logging setup, file operations, configuration management, and data structures.
"""

import json
import logging
import os
import re
import subprocess
import tempfile
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from rich.console import Console
from rich.logging import RichHandler
from rich.progress import Progress, SpinnerColumn, TextColumn


class PropertyGPTError(Exception):
    """Base exception for PropertyGPT errors."""
    pass


class CompilationError(PropertyGPTError):
    """Raised when Solidity compilation fails."""
    pass


class ProverError(PropertyGPTError):
    """Raised when SMT proving fails."""
    pass


@dataclass
class InvariantProperty:
    """Represents a generated invariant property."""
    id: str
    description: str
    code: str
    function_name: str
    property_type: str  # "precondition", "postcondition", "cross_invariant"
    confidence_score: float = 0.0
    compilation_status: str = "unknown"  # "success", "failed", "not_attempted"
    proof_status: str = "unknown"  # "verified", "violated", "timeout", "unknown"
    counter_example: Optional[Dict[str, Any]] = None


@dataclass
class ContractFunction:
    """Represents a contract function with metadata."""
    name: str
    signature: str
    visibility: str
    state_mutability: str
    parameters: List[Dict[str, str]]
    returns: List[Dict[str, str]]


@dataclass
class SimilarProperty:
    """Represents a similar property from the knowledge base."""
    content: str
    similarity_score: float
    source_file: str
    context: str


def setup_logging(verbose: bool = False) -> Console:
    """
    Set up logging with Rich formatting.
    
    Args:
        verbose: Enable debug logging if True
        
    Returns:
        Rich console instance for additional output
    """
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(rich_tracebacks=True)]
    )
    
    # Suppress noisy third-party loggers
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    return Console()


def find_spec_files(directory: Path) -> List[Path]:
    """
    Recursively find all specification files in a directory.
    
    Args:
        directory: Directory to search
        
    Returns:
        List of paths to spec files (.spec, .cvl, .sld files)
    """
    spec_extensions = {".spec", ".cvl", ".sld"}
    spec_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if any(file.endswith(ext) for ext in spec_extensions):
                spec_files.append(Path(root) / file)
    
    return spec_files


def read_file_safe(file_path: Path) -> Optional[str]:
    """
    Safely read a file with error handling.
    
    Args:
        file_path: Path to the file
        
    Returns:
        File content or None if reading failed
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.read()
    except Exception as e:
        logging.warning(f"Failed to read {file_path}: {e}")
        return None


def extract_functions_from_spec(content: str) -> List[str]:
    """
    Extract function signatures and rules from specification content.
    
    Args:
        content: Specification file content
        
    Returns:
        List of extracted function/rule descriptions
    """
    functions = []
    
    # Extract method declarations
    method_pattern = r'methods\s*\{([^}]+)\}'
    method_match = re.search(method_pattern, content, re.DOTALL)
    if method_match:
        methods_block = method_match.group(1)
        # Extract individual method signatures
        method_lines = [line.strip() for line in methods_block.split('\n') if line.strip()]
        functions.extend(method_lines)
    
    # Extract rule definitions
    rule_pattern = r'rule\s+(\w+)\s*\([^)]*\)\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}'
    for match in re.finditer(rule_pattern, content, re.DOTALL):
        rule_name = match.group(1)
        rule_body = match.group(2)
        functions.append(f"rule {rule_name}: {rule_body[:200]}...")
    
    # Extract invariant definitions
    invariant_pattern = r'invariant\s+(\w+)\s*\([^)]*\)\s*([^;]+);'
    for match in re.finditer(invariant_pattern, content):
        inv_name = match.group(1)
        inv_body = match.group(2)
        functions.append(f"invariant {inv_name}: {inv_body}")
    
    return functions


def run_command(cmd: List[str], cwd: Optional[Path] = None, timeout: int = 60) -> Tuple[int, str, str]:
    """
    Run a shell command with timeout and error handling.
    
    Args:
        cmd: Command and arguments as list
        cwd: Working directory
        timeout: Timeout in seconds
        
    Returns:
        Tuple of (return_code, stdout, stderr)
    """
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", f"Command timed out after {timeout} seconds"
    except Exception as e:
        return -1, "", str(e)


def create_temp_file(content: str, suffix: str = ".sol") -> Path:
    """
    Create a temporary file with given content.
    
    Args:
        content: File content
        suffix: File extension
        
    Returns:
        Path to the temporary file
    """
    with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
        f.write(content)
        return Path(f.name)


def parse_solidity_errors(stderr: str) -> List[Dict[str, Any]]:
    """
    Parse Solidity compiler errors from stderr.
    
    Args:
        stderr: Compiler error output
        
    Returns:
        List of parsed error dictionaries
    """
    errors = []
    
    # Pattern for Solidity errors
    error_pattern = r'(\w+\.sol):(\d+):(\d+): (\w+): (.+)'
    
    for line in stderr.split('\n'):
        match = re.match(error_pattern, line.strip())
        if match:
            errors.append({
                'file': match.group(1),
                'line': int(match.group(2)),
                'column': int(match.group(3)),
                'severity': match.group(4),
                'message': match.group(5)
            })
    
    return errors


def calculate_edit_distance(s1: str, s2: str) -> int:
    """
    Calculate Levenshtein edit distance between two strings.
    
    Args:
        s1, s2: Strings to compare
        
    Returns:
        Edit distance
    """
    if len(s1) < len(s2):
        return calculate_edit_distance(s2, s1)
    
    if len(s2) == 0:
        return len(s1)
    
    previous_row = list(range(len(s2) + 1))
    for i, c1 in enumerate(s1):
        current_row = [i + 1]
        for j, c2 in enumerate(s2):
            insertions = previous_row[j + 1] + 1
            deletions = current_row[j] + 1
            substitutions = previous_row[j] + (c1 != c2)
            current_row.append(min(insertions, deletions, substitutions))
        previous_row = current_row
    
    return previous_row[-1]


def calculate_keyword_coverage(text: str, keywords: List[str]) -> float:
    """
    Calculate keyword coverage score for text.
    
    Args:
        text: Text to analyze
        keywords: List of keywords to check
        
    Returns:
        Coverage score between 0 and 1
    """
    if not keywords:
        return 0.0
    
    text_lower = text.lower()
    covered = sum(1 for keyword in keywords if keyword.lower() in text_lower)
    return covered / len(keywords)


def calculate_complexity_score(code: str) -> float:
    """
    Calculate complexity score for code based on various metrics.
    
    Args:
        code: Code to analyze
        
    Returns:
        Complexity score
    """
    # Simple complexity metrics
    lines = len([line for line in code.split('\n') if line.strip()])
    operators = len(re.findall(r'[+\-*/=<>!&|]', code))
    keywords = len(re.findall(r'\b(if|else|for|while|require|assert)\b', code))
    
    # Normalize and combine metrics
    complexity = (lines * 0.1) + (operators * 0.05) + (keywords * 0.2)
    return min(complexity, 10.0)  # Cap at 10


def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def ensure_directory(path: Path) -> None:
    """
    Ensure directory exists, creating it if necessary.
    
    Args:
        path: Directory path
    """
    path.mkdir(parents=True, exist_ok=True)


def load_json_safe(file_path: Path) -> Optional[Dict[str, Any]]:
    """
    Safely load JSON file with error handling.
    
    Args:
        file_path: Path to JSON file
        
    Returns:
        Parsed JSON data or None if loading failed
    """
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.warning(f"Failed to load JSON from {file_path}: {e}")
        return None


def save_json_safe(data: Dict[str, Any], file_path: Path) -> bool:
    """
    Safely save data to JSON file.
    
    Args:
        data: Data to save
        file_path: Output file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        ensure_directory(file_path.parent)
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logging.error(f"Failed to save JSON to {file_path}: {e}")
        return False
