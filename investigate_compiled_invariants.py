#!/usr/bin/env python3
"""
Investigate what invariants actually passed compilation.
"""

import os
from pathlib import Path
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def investigate_compiled_invariants():
    """Check what invariants actually passed compilation."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        print("🔍 Running a quick analysis to see what gets compiled...")
        
        # Set the contract path for context extraction
        generator._current_contract_path = Path("demo/Bank.sol")
        
        # Test with one function to see the compilation process
        test_function = ContractFunction(
            name="setOwner",
            signature="setOwner(address)",
            visibility="external",
            state_mutability="nonpayable",
            parameters=[{"name": "newOwner", "type": "address"}],
            returns=[]
        )
        
        print(f"🧠 Generating invariants for setOwner function...")
        
        # Generate invariants
        raw_invariants = generator._generate_function_invariants(test_function, [], k_similar=3, max_retries=1)
        print(f"✅ Generated {len(raw_invariants)} raw invariants")
        
        print(f"\n🔧 Testing compilation for each invariant...")
        
        compiled_count = 0
        for i, inv in enumerate(raw_invariants):
            print(f"\n--- Raw Invariant {i+1} ---")
            print(f"Code: {inv.code}")
            
            # Test compilation
            compiled_inv = generator._compile_and_fix_invariant(inv, Path("demo/Bank.sol"), max_retries=1)
            
            if compiled_inv and compiled_inv.compilation_status == "success":
                compiled_count += 1
                print(f"✅ PASSED compilation")
                print(f"Final code: {compiled_inv.code}")
                
                # Save the test contract for this invariant
                test_contract = generator._create_test_contract(inv, Path("demo/Bank.sol"))
                with open(f"compiled_invariant_{i+1}.sol", "w") as f:
                    f.write(test_contract)
                print(f"💾 Saved test contract to compiled_invariant_{i+1}.sol")
                
            elif compiled_inv and compiled_inv.compilation_status == "failed":
                print(f"❌ FAILED compilation: {getattr(compiled_inv, 'compilation_error', 'No error details')}")
            else:
                print(f"❌ FAILED compilation: Returned None")
        
        print(f"\n📊 Summary: {compiled_count}/{len(raw_invariants)} invariants passed compilation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Investigating Compiled Invariants")
    print("=" * 50)
    
    success = investigate_compiled_invariants()
    
    print("\n" + "=" * 50)
    if success:
        print("🎯 Investigation completed")
    else:
        print("❌ Investigation failed")
