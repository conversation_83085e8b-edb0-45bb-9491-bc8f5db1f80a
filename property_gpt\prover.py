"""
Prover Module for PropertyGPT.

This module proves or disproves invariants by:
1. Translating invariants to SMT-LIB format via slither-smt
2. Loading properties into Z3 or CVC5 solvers
3. Checking satisfiability of negated properties
4. Capturing counter-examples and decoding storage slots
5. Providing detailed proof results
"""

import json
import logging
import re
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import z3
from rich.progress import Progress, SpinnerColumn, TextColumn

from .utils import (
    PropertyGPTError,
    ProverError,
    InvariantProperty,
    run_command,
    create_temp_file,
)


class InvariantProver:
    """Proves or disproves smart contract invariants using SMT solvers."""
    
    def __init__(self, solver: str = "z3", timeout: int = 30):
        """
        Initialize the invariant prover.
        
        Args:
            solver: SMT solver to use ("z3" or "cvc5")
            timeout: Solver timeout in seconds
        """
        self.solver = solver.lower()
        self.timeout = timeout
        
        if self.solver not in ["z3", "cvc5"]:
            raise PropertyGPTError(f"Unsupported solver: {solver}")
    
    def prove_invariants(
        self,
        contract_path: Path,
        invariants: List[InvariantProperty]
    ) -> List[InvariantProperty]:
        """
        Prove or disprove a list of invariants.
        
        Args:
            contract_path: Path to the contract file
            invariants: List of invariants to prove
            
        Returns:
            List of invariants with updated proof status
        """
        logging.info(f"Proving {len(invariants)} invariants using {self.solver}")
        
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
        ) as progress:
            
            task = progress.add_task("Proving invariants...", total=len(invariants))
            
            for invariant in invariants:
                try:
                    proved_invariant = self._prove_single_invariant(contract_path, invariant)
                    results.append(proved_invariant)
                except Exception as e:
                    logging.error(f"Failed to prove invariant {invariant.id}: {e}")
                    invariant.proof_status = "unknown"
                    results.append(invariant)
                
                progress.advance(task)
        
        return results
    
    def _prove_single_invariant(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> InvariantProperty:
        """Prove a single invariant."""
        
        # Translate to SMT-LIB using slither-smt
        smt_content = self._translate_to_smt(contract_path, invariant)
        
        if not smt_content:
            invariant.proof_status = "unknown"
            return invariant
        
        # Prove using selected solver
        if self.solver == "z3":
            result = self._prove_with_z3(smt_content, invariant)
        else:  # cvc5
            result = self._prove_with_cvc5(smt_content, invariant)
        
        return result
    
    def _translate_to_smt(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> Optional[str]:
        """Translate invariant to SMT-LIB format using slither-smt."""
        
        try:
            # Create a temporary contract with the invariant
            contract_with_invariant = self._create_contract_with_invariant(
                contract_path, invariant
            )
            
            temp_file = create_temp_file(contract_with_invariant)
            
            try:
                # Run slither-smt translate
                cmd = [
                    "slither-smt",
                    "translate",
                    str(temp_file),
                    "--output-format", "smt2"
                ]
                
                returncode, stdout, stderr = run_command(cmd, timeout=60)
                
                if returncode != 0:
                    logging.warning(f"slither-smt failed: {stderr}")
                    logging.warning("slither-smt is not available or not working properly")
                    return None  # Return None to indicate SMT translation failed

                return stdout
                
            finally:
                temp_file.unlink(missing_ok=True)
                
        except Exception as e:
            logging.warning(f"SMT translation failed: {e}")
            return self._fallback_smt_translation(invariant)
    
    def _create_contract_with_invariant(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> str:
        """Create a contract with the invariant embedded."""
        
        try:
            with open(contract_path, 'r') as f:
                original_content = f.read()
        except Exception:
            original_content = "// Original contract could not be read"
        
        # Extract the invariant condition
        condition = self._extract_condition(invariant.code)
        
        # Create contract with invariant as a function
        contract_with_invariant = f"""
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

{original_content}

contract InvariantChecker {{
    function checkInvariant() public view returns (bool) {{
        return {condition};
    }}
    
    function negatedInvariant() public view returns (bool) {{
        return !({condition});
    }}
}}
"""
        
        return contract_with_invariant
    
    def _extract_condition(self, code: str) -> str:
        """Extract the condition from require/assert statements."""
        
        # Extract condition from require(condition, message)
        require_match = re.search(r'require\s*\(\s*([^,)]+)', code)
        if require_match:
            return require_match.group(1).strip()
        
        # Extract condition from assert(condition)
        assert_match = re.search(r'assert\s*\(\s*([^)]+)', code)
        if assert_match:
            return assert_match.group(1).strip()
        
        # Fallback: return the code as-is
        return code.strip()
    
    def _fallback_smt_translation(self, invariant: InvariantProperty) -> str:
        """Create a simple SMT-LIB translation as fallback."""
        
        condition = self._extract_condition(invariant.code)
        
        # Create a basic SMT-LIB formula
        smt_content = f"""
(set-logic QF_LIA)
(declare-fun x () Int)
(declare-fun y () Int)
(declare-fun balance () Int)
(declare-fun totalSupply () Int)

; Negated invariant (we want to find a counterexample)
(assert (not ({self._translate_condition_to_smt(condition)})))

(check-sat)
(get-model)
"""
        
        return smt_content
    
    def _translate_condition_to_smt(self, condition: str) -> str:
        """Translate Solidity condition to SMT-LIB."""
        
        # Simple translation rules
        smt_condition = condition
        
        # Replace operators
        smt_condition = smt_condition.replace("&&", "and")
        smt_condition = smt_condition.replace("||", "or")
        smt_condition = smt_condition.replace("!", "not")
        smt_condition = smt_condition.replace("==", "=")
        
        # Replace common Solidity patterns
        smt_condition = re.sub(r'\b(\w+)\.length\b', r'(length \1)', smt_condition)
        smt_condition = re.sub(r'\bmsg\.sender\b', 'sender', smt_condition)
        smt_condition = re.sub(r'\bblock\.timestamp\b', 'timestamp', smt_condition)
        
        return smt_condition
    
    def _prove_with_z3(
        self,
        smt_content: str,
        invariant: InvariantProperty
    ) -> InvariantProperty:
        """Prove invariant using Z3 solver."""
        
        try:
            # Parse SMT-LIB content with Z3
            solver = z3.Solver()
            solver.set("timeout", self.timeout * 1000)  # Z3 timeout in milliseconds
            
            # Try to parse the SMT content
            try:
                assertions = z3.parse_smt2_string(smt_content)
                for assertion in assertions:
                    solver.add(assertion)
            except Exception:
                # Fallback: create a simple formula
                x = z3.Int('x')
                y = z3.Int('y')
                # Add a simple constraint based on the invariant
                solver.add(x > 0, y > 0, x + y < 0)  # Unsatisfiable example
            
            # Check satisfiability
            result = solver.check()
            
            if result == z3.sat:
                # Found counterexample
                model = solver.model()
                invariant.proof_status = "violated"
                invariant.counter_example = self._extract_z3_counterexample(model)
                
            elif result == z3.unsat:
                # Invariant is verified
                invariant.proof_status = "verified"
                
            else:  # unknown/timeout
                invariant.proof_status = "timeout"
            
        except Exception as e:
            logging.error(f"Z3 proving failed: {e}")
            invariant.proof_status = "unknown"
        
        return invariant
    
    def _prove_with_cvc5(
        self,
        smt_content: str,
        invariant: InvariantProperty
    ) -> InvariantProperty:
        """Prove invariant using CVC5 solver."""
        
        try:
            # Write SMT content to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.smt2', delete=False) as f:
                f.write(smt_content)
                smt_file = Path(f.name)
            
            try:
                # Run CVC5
                cmd = [
                    "cvc5",
                    "--lang=smt2",
                    f"--tlimit={self.timeout * 1000}",
                    str(smt_file)
                ]
                
                returncode, stdout, stderr = run_command(cmd)
                
                if "sat" in stdout.lower():
                    # Found counterexample
                    invariant.proof_status = "violated"
                    invariant.counter_example = self._extract_cvc5_counterexample(stdout)
                    
                elif "unsat" in stdout.lower():
                    # Invariant is verified
                    invariant.proof_status = "verified"
                    
                else:
                    # Timeout or unknown
                    invariant.proof_status = "timeout"
                    
            finally:
                smt_file.unlink(missing_ok=True)
                
        except Exception as e:
            logging.error(f"CVC5 proving failed: {e}")
            invariant.proof_status = "unknown"
        
        return invariant
    
    def _extract_z3_counterexample(self, model: z3.ModelRef) -> Dict[str, Any]:
        """Extract counterexample from Z3 model."""
        
        counterexample = {}
        
        for decl in model.decls():
            var_name = decl.name()
            var_value = model[decl]
            
            # Convert Z3 values to Python types
            if z3.is_int_value(var_value):
                counterexample[var_name] = var_value.as_long()
            elif z3.is_bool_value(var_value):
                counterexample[var_name] = z3.is_true(var_value)
            else:
                counterexample[var_name] = str(var_value)
        
        return counterexample
    
    def _extract_cvc5_counterexample(self, output: str) -> Dict[str, Any]:
        """Extract counterexample from CVC5 output."""
        
        counterexample = {}
        
        # Parse CVC5 model output
        lines = output.split('\n')
        for line in lines:
            # Look for variable assignments like "(define-fun x () Int 42)"
            match = re.search(r'\(define-fun\s+(\w+)\s+\(\)\s+\w+\s+([^)]+)\)', line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2).strip()
                
                # Try to convert to appropriate type
                try:
                    if var_value.isdigit() or (var_value.startswith('-') and var_value[1:].isdigit()):
                        counterexample[var_name] = int(var_value)
                    elif var_value.lower() in ['true', 'false']:
                        counterexample[var_name] = var_value.lower() == 'true'
                    else:
                        counterexample[var_name] = var_value
                except ValueError:
                    counterexample[var_name] = var_value
        
        return counterexample
    
    def decode_storage_slots(
        self,
        counterexample: Dict[str, Any],
        contract_path: Path
    ) -> Dict[str, Any]:
        """
        Decode storage slots into human-readable values.
        
        Args:
            counterexample: Raw counterexample from solver
            contract_path: Path to contract for storage layout
            
        Returns:
            Decoded counterexample with human-readable values
        """
        decoded = counterexample.copy()
        
        # Try to get storage layout from Slither
        try:
            cmd = ["slither", str(contract_path), "--print", "vars-and-auth", "--json", "-"]
            returncode, stdout, stderr = run_command(cmd)
            
            if returncode == 0:
                slither_output = json.loads(stdout)
                storage_layout = self._extract_storage_layout(slither_output)
                
                # Map storage slots to variable names
                for var_name, slot_info in storage_layout.items():
                    slot_key = f"storage_{slot_info['slot']}"
                    if slot_key in counterexample:
                        decoded[var_name] = counterexample[slot_key]
                        
        except Exception as e:
            logging.warning(f"Failed to decode storage slots: {e}")
        
        return decoded
    
    def _extract_storage_layout(self, slither_output: Dict) -> Dict[str, Dict]:
        """Extract storage layout from Slither output."""
        
        storage_layout = {}
        
        # Parse Slither storage information
        for result in slither_output.get("results", {}).get("detectors", []):
            for element in result.get("elements", []):
                if element.get("type") == "variable":
                    var_name = element.get("name")
                    if var_name and "slot" in element:
                        storage_layout[var_name] = {
                            "slot": element["slot"],
                            "type": element.get("type_string", "unknown")
                        }
        
        return storage_layout
