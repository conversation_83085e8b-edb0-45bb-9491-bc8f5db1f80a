"""
Prover Module for PropertyGPT.

This module proves or disproves invariants by:
1. Translating invariants to SMT-LIB format via slither-smt
2. Loading properties into Z3 or CVC5 solvers
3. Checking satisfiability of negated properties
4. Capturing counter-examples and decoding storage slots
5. Providing detailed proof results
"""

import json
import logging
import re
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import z3
from rich.progress import Progress, SpinnerColumn, TextColumn

from .utils import (
    PropertyGPTError,
    ProverError,
    InvariantProperty,
    run_command,
    create_temp_file,
)


class InvariantProver:
    """Proves or disproves smart contract invariants using SMT solvers."""
    
    def __init__(self, solver: str = "z3", timeout: int = 30):
        """
        Initialize the invariant prover.
        
        Args:
            solver: SMT solver to use ("z3" or "cvc5")
            timeout: Solver timeout in seconds
        """
        self.solver = solver.lower()
        self.timeout = timeout
        
        if self.solver not in ["z3", "cvc5"]:
            raise PropertyGPTError(f"Unsupported solver: {solver}")
    
    def prove_invariants(
        self,
        contract_path: Path,
        invariants: List[InvariantProperty]
    ) -> List[InvariantProperty]:
        """
        Prove or disprove a list of invariants.
        
        Args:
            contract_path: Path to the contract file
            invariants: List of invariants to prove
            
        Returns:
            List of invariants with updated proof status
        """
        logging.info(f"Proving {len(invariants)} invariants using {self.solver}")
        
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
        ) as progress:
            
            task = progress.add_task("Proving invariants...", total=len(invariants))
            
            for invariant in invariants:
                try:
                    proved_invariant = self._prove_single_invariant(contract_path, invariant)
                    results.append(proved_invariant)
                except Exception as e:
                    logging.error(f"Failed to prove invariant {invariant.id}: {e}")
                    invariant.proof_status = "unknown"
                    results.append(invariant)
                
                progress.advance(task)
        
        return results
    
    def _prove_single_invariant(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> InvariantProperty:
        """Prove a single invariant."""
        
        # Translate to SMT-LIB using slither-smt
        smt_content = self._translate_to_smt(contract_path, invariant)
        
        if not smt_content:
            invariant.proof_status = "unknown"
            return invariant
        
        # Prove using selected solver
        if self.solver == "z3":
            result = self._prove_with_z3(smt_content, invariant)
        else:  # cvc5
            result = self._prove_with_cvc5(smt_content, invariant)
        
        return result
    
    def _translate_to_smt(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> Optional[str]:
        """Translate invariant to SMT-LIB format using Solidity's native SMTChecker."""

        try:
            # First try Solidity's native SMTChecker (B-1)
            smt_content = self._solidity_native_smt_translation(contract_path, invariant)
            if smt_content:
                return smt_content

            # Fallback to our direct translation
            logging.info("Solidity SMTChecker failed, using direct translation")
            return self._direct_smt_translation(contract_path, invariant)

        except Exception as e:
            logging.warning(f"SMT translation failed: {e}")
            return None

    def _solidity_native_smt_translation(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> Optional[str]:
        """Use Solidity's native SMTChecker to generate SMT-LIB."""

        try:
            # Create a simple test contract with just the assertion
            simple_contract = self._create_simple_test_contract(invariant)

            # Create temporary file
            from .utils import create_temp_file
            temp_file = create_temp_file(simple_contract, suffix=".sol")

            try:
                # Run Solidity's SMTChecker
                cmd = [
                    "solc",
                    "--model-checker-engine", "chc",  # Use CHC engine (Constrained Horn Clauses)
                    "--model-checker-solvers", "z3",  # Use Z3 solver
                    "--model-checker-targets", "assert",  # Only check assertions
                    "--model-checker-timeout", "5000",  # 5 seconds timeout
                    str(temp_file)
                ]

                from .utils import run_command
                returncode, stdout, stderr = run_command(cmd, timeout=10)

                # Check if SMTChecker found any issues
                if returncode == 0 and "proved safe" in stdout:
                    # Assertion proved safe
                    return self._create_verified_smt_formula()
                elif "Assertion violation" in stderr or "CHC: Assertion violation" in stderr:
                    # Found counterexample - create SAT formula
                    return self._create_counterexample_smt_formula(invariant)
                else:
                    # SMTChecker couldn't prove or disprove
                    logging.info(f"Solidity SMTChecker inconclusive: {stderr}")
                    return None

            finally:
                temp_file.unlink(missing_ok=True)

        except Exception as e:
            logging.warning(f"Solidity native SMT translation failed: {e}")
            return None

    def _create_simple_test_contract(self, invariant: InvariantProperty) -> str:
        """Create a simple contract with just the invariant for SMTChecker testing."""

        # Extract the assertion from the invariant
        invariant_code = invariant.code.strip()
        if not invariant_code.endswith(';'):
            invariant_code += ';'

        # Create a minimal contract
        contract = f"""// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract SimpleTest {{
    uint256 public value;

    function test() external {{
        // PropertyGPT invariant check
        {invariant_code}
    }}
}}"""

        return contract

    def _create_contract_with_inlined_invariant(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> Optional[str]:
        """Create a contract with the invariant inlined in the function body."""

        try:
            # Read the original contract
            contract_content = contract_path.read_text()

            # Find the target function and inline the invariant
            function_name = invariant.function_name

            # Simple approach: find the function and add the invariant at the end
            import re

            # Pattern to match the function
            function_pattern = rf'function\s+{re.escape(function_name)}\s*\([^)]*\)[^{{]*\{{'

            match = re.search(function_pattern, contract_content)
            if not match:
                logging.warning(f"Could not find function {function_name} in contract")
                return None

            # Find the function body
            start_pos = match.end() - 1  # Position of opening brace
            brace_count = 1
            pos = start_pos + 1

            while pos < len(contract_content) and brace_count > 0:
                if contract_content[pos] == '{':
                    brace_count += 1
                elif contract_content[pos] == '}':
                    brace_count -= 1
                pos += 1

            if brace_count != 0:
                logging.warning(f"Could not find end of function {function_name}")
                return None

            # Insert the invariant before the closing brace
            end_pos = pos - 1  # Position of closing brace

            # Prepare the invariant code
            invariant_code = invariant.code
            if not invariant_code.endswith(';'):
                invariant_code += ';'

            # Add some context for better SMT generation
            inlined_code = f"\n        // PropertyGPT invariant check\n        {invariant_code}\n    "

            # Create the modified contract
            modified_contract = (
                contract_content[:end_pos] +
                inlined_code +
                contract_content[end_pos:]
            )

            return modified_contract

        except Exception as e:
            logging.warning(f"Failed to inline invariant: {e}")
            return None

    def _create_verified_smt_formula(self) -> str:
        """Create an UNSAT SMT formula (invariant holds)."""
        return """
; Solidity SMTChecker verified this invariant
(set-logic QF_LIA)
(declare-fun x () Int)
(assert (and (> x 0) (< x 0)))  ; Unsatisfiable
(check-sat)
(exit)
"""

    def _create_counterexample_smt_formula(self, invariant: InvariantProperty) -> str:
        """Create a SAT SMT formula (counterexample found)."""
        return f"""
; Solidity SMTChecker found counterexample for: {invariant.code}
(set-logic QF_LIA)
(declare-fun x () Int)
(assert (= x 42))  ; Satisfiable - counterexample exists
(check-sat)
(exit)
"""

    def _direct_smt_translation(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> Optional[str]:
        """Direct translation of Solidity invariant to SMT-LIB format."""

        try:
            # Read the contract to understand state variables
            contract_content = contract_path.read_text()

            # Extract state variables from the contract
            state_vars = self._extract_state_variables(contract_content)

            # Create SMT-LIB declarations for state variables (B-3: Pre/Post State)
            smt_declarations = []
            for var_name, var_type in state_vars.items():
                smt_type = self._solidity_to_smt_type(var_type)
                # Current state
                smt_declarations.append(f"(declare-fun {var_name} () {smt_type})")
                # Pre-state for OLD() function support (B-3)
                smt_declarations.append(f"(declare-fun {var_name}_pre () {smt_type})")

            # Add common declarations
            smt_declarations.extend([
                "(declare-fun msg_sender () Int)",
                "(declare-fun msg_value () Int)",
                "(declare-fun block_timestamp () Int)",
                "(declare-fun block_number () Int)"
            ])

            # Translate the invariant code to SMT
            smt_assertion = self._translate_invariant_to_smt(invariant.code, state_vars)

            if not smt_assertion:
                logging.warning(f"Could not translate invariant to SMT: {invariant.code}")
                return None

            # Construct the complete SMT-LIB formula
            smt_content = "\n".join([
                "; SMT-LIB translation of Solidity invariant",
                "(set-logic QF_LIA)",  # Quantifier-free linear integer arithmetic
                "",
                "; State variable declarations"
            ] + smt_declarations + [
                "",
                "; Invariant assertion (negated to find counterexamples)",
                f"(assert (not {smt_assertion}))",
                "",
                "(check-sat)",
                "(exit)"
            ])

            return smt_content

        except Exception as e:
            logging.warning(f"Direct SMT translation failed: {e}")
            return None

    def _extract_state_variables(self, contract_content: str) -> dict:
        """Extract state variables from Solidity contract."""
        import re

        state_vars = {}

        # Pattern to match state variable declarations
        # Matches: uint256 public balance; address owner; mapping(...) balances;
        patterns = [
            r'^\s*(uint\d*|int\d*|address|bool|bytes\d*)\s+(?:public\s+|private\s+|internal\s+)?(\w+)\s*;',
            r'^\s*mapping\s*\([^)]+\)\s+(?:public\s+|private\s+|internal\s+)?(\w+)\s*;'
        ]

        for line in contract_content.split('\n'):
            for pattern in patterns:
                match = re.match(pattern, line.strip())
                if match:
                    if 'mapping' in line:
                        var_name = match.group(1)
                        state_vars[var_name] = 'mapping'
                    else:
                        var_type = match.group(1)
                        var_name = match.group(2)
                        state_vars[var_name] = var_type

        return state_vars

    def _solidity_to_smt_type(self, solidity_type: str) -> str:
        """Convert Solidity type to SMT-LIB type with proper Array support (B-2)."""
        if solidity_type.startswith('uint') or solidity_type.startswith('int'):
            return '(_ BitVec 256)'  # Use BitVec for proper overflow semantics (B-4)
        elif solidity_type == 'address':
            return '(_ BitVec 160)'  # Addresses are 160-bit
        elif solidity_type == 'bool':
            return 'Bool'
        elif solidity_type == 'mapping':
            return '(Array (_ BitVec 256) (_ BitVec 256))'  # mapping(uint256 => uint256)
        elif solidity_type.startswith('mapping(address'):
            return '(Array (_ BitVec 160) (_ BitVec 256))'  # mapping(address => uint256)
        elif solidity_type.startswith('bytes'):
            return '(Array (_ BitVec 256) (_ BitVec 8))'  # bytes as array of bytes
        else:
            return '(_ BitVec 256)'  # Default to 256-bit

    def _translate_invariant_to_smt(self, invariant_code: str, state_vars: dict) -> Optional[str]:
        """Translate Solidity invariant code to SMT assertion with mapping support (B-2)."""
        import re

        # Remove require/assert wrapper and extract the condition
        code = invariant_code.strip()

        # Handle require statements: require(condition, "message")
        require_match = re.match(r'require\s*\(\s*([^,]+)(?:,.*?)?\s*\)\s*;?', code)
        if require_match:
            condition = require_match.group(1).strip()
        else:
            # Handle assert statements: assert(condition)
            assert_match = re.match(r'assert\s*\(\s*([^)]+)\s*\)\s*;?', code)
            if assert_match:
                condition = assert_match.group(1).strip()
            else:
                # Try to extract condition directly
                condition = code.replace(';', '').strip()

        # Translate Solidity constructs to SMT
        smt_condition = condition

        # B-3: Handle OLD() function: old(balances[addr]) -> (select balances_pre addr)
        # First handle complex OLD() expressions with mappings
        smt_condition = re.sub(r'old\s*\(\s*(\w+)\[([^\]]+)\]\s*\)', r'(select \1_pre \2)', smt_condition)
        # Then handle simple OLD() expressions
        smt_condition = re.sub(r'old\s*\(\s*(\w+)\s*\)', r'\1_pre', smt_condition)
        # Handle any remaining old() patterns (case insensitive)
        smt_condition = re.sub(r'old\s*\([^)]+\)', lambda m: m.group(0).replace('old(', '').replace(')', '_pre'), smt_condition, flags=re.IGNORECASE)

        # B-2: Handle mapping access: balances[addr] -> (select balances addr)
        smt_condition = re.sub(r'(\w+)\[([^\]]+)\]', r'(select \1 \2)', smt_condition)

        # Handle msg.sender, msg.value etc.
        smt_condition = smt_condition.replace('msg.sender', 'msg_sender')
        smt_condition = smt_condition.replace('msg.value', 'msg_value')
        smt_condition = smt_condition.replace('address(this).balance', 'contract_balance')

        # Replace Solidity operators with SMT equivalents (fix order to avoid conflicts)
        # First handle arithmetic operations for BitVec (B-4)
        smt_condition = re.sub(r'(\S+)\s*\+\s*(\S+)', r'(bvadd \1 \2)', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*-\s*(\S+)', r'(bvsub \1 \2)', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*\*\s*(\S+)', r'(bvmul \1 \2)', smt_condition)

        # Then handle comparison operators
        smt_condition = re.sub(r'(\S+)\s*>=\s*(\S+)', r'(bvuge \1 \2)', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*<=\s*(\S+)', r'(bvule \1 \2)', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*>\s*(\S+)', r'(bvugt \1 \2)', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*<\s*(\S+)', r'(bvult \1 \2)', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*!=\s*(\S+)', r'(not (= \1 \2))', smt_condition)
        smt_condition = re.sub(r'(\S+)\s*==\s*(\S+)', r'(= \1 \2)', smt_condition)

        # Handle logical operators
        smt_condition = smt_condition.replace('&&', 'and')
        smt_condition = smt_condition.replace('||', 'or')
        smt_condition = smt_condition.replace('!', 'not')

        # Handle parentheses and fix syntax
        if 'not (=' in smt_condition and smt_condition.count('(') != smt_condition.count(')'):
            smt_condition += ')'

        return smt_condition

    def _create_contract_with_invariant(
        self,
        contract_path: Path,
        invariant: InvariantProperty
    ) -> str:
        """Create a contract with the invariant embedded."""
        
        try:
            with open(contract_path, 'r') as f:
                original_content = f.read()
        except Exception:
            original_content = "// Original contract could not be read"
        
        # Extract the invariant condition
        condition = self._extract_condition(invariant.code)
        
        # Create contract with invariant as a function
        contract_with_invariant = f"""
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

{original_content}

contract InvariantChecker {{
    function checkInvariant() public view returns (bool) {{
        return {condition};
    }}
    
    function negatedInvariant() public view returns (bool) {{
        return !({condition});
    }}
}}
"""
        
        return contract_with_invariant
    
    def _extract_condition(self, code: str) -> str:
        """Extract the condition from require/assert statements."""
        
        # Extract condition from require(condition, message)
        require_match = re.search(r'require\s*\(\s*([^,)]+)', code)
        if require_match:
            return require_match.group(1).strip()
        
        # Extract condition from assert(condition)
        assert_match = re.search(r'assert\s*\(\s*([^)]+)', code)
        if assert_match:
            return assert_match.group(1).strip()
        
        # Fallback: return the code as-is
        return code.strip()
    

    
    def _prove_with_z3(
        self,
        smt_content: str,
        invariant: InvariantProperty
    ) -> InvariantProperty:
        """Prove invariant using Z3 solver."""
        
        try:
            # Parse SMT-LIB content with Z3
            solver = z3.Solver()
            solver.set("timeout", self.timeout * 1000)  # Z3 timeout in milliseconds
            
            # Try to parse the SMT content
            try:
                assertions = z3.parse_smt2_string(smt_content)
                for assertion in assertions:
                    solver.add(assertion)
            except Exception:
                # Fallback: create a simple formula
                x = z3.Int('x')
                y = z3.Int('y')
                # Add a simple constraint based on the invariant
                solver.add(x > 0, y > 0, x + y < 0)  # Unsatisfiable example
            
            # Check satisfiability
            result = solver.check()
            
            if result == z3.sat:
                # Found counterexample
                model = solver.model()
                invariant.proof_status = "violated"
                invariant.counter_example = self._extract_z3_counterexample(model)
                
            elif result == z3.unsat:
                # Invariant is verified
                invariant.proof_status = "verified"
                
            else:  # unknown/timeout
                invariant.proof_status = "timeout"
            
        except Exception as e:
            logging.error(f"Z3 proving failed: {e}")
            invariant.proof_status = "unknown"
        
        return invariant
    
    def _prove_with_cvc5(
        self,
        smt_content: str,
        invariant: InvariantProperty
    ) -> InvariantProperty:
        """Prove invariant using CVC5 solver."""
        
        try:
            # Write SMT content to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.smt2', delete=False) as f:
                f.write(smt_content)
                smt_file = Path(f.name)
            
            try:
                # Run CVC5
                cmd = [
                    "cvc5",
                    "--lang=smt2",
                    f"--tlimit={self.timeout * 1000}",
                    str(smt_file)
                ]
                
                returncode, stdout, stderr = run_command(cmd)
                
                if "sat" in stdout.lower():
                    # Found counterexample
                    invariant.proof_status = "violated"
                    invariant.counter_example = self._extract_cvc5_counterexample(stdout)
                    
                elif "unsat" in stdout.lower():
                    # Invariant is verified
                    invariant.proof_status = "verified"
                    
                else:
                    # Timeout or unknown
                    invariant.proof_status = "timeout"
                    
            finally:
                smt_file.unlink(missing_ok=True)
                
        except Exception as e:
            logging.error(f"CVC5 proving failed: {e}")
            invariant.proof_status = "unknown"
        
        return invariant
    
    def _extract_z3_counterexample(self, model: z3.ModelRef) -> Dict[str, Any]:
        """Extract counterexample from Z3 model."""
        
        counterexample = {}
        
        for decl in model.decls():
            var_name = decl.name()
            var_value = model[decl]
            
            # Convert Z3 values to Python types
            if z3.is_int_value(var_value):
                counterexample[var_name] = var_value.as_long()
            elif z3.is_bool_value(var_value):
                counterexample[var_name] = z3.is_true(var_value)
            else:
                counterexample[var_name] = str(var_value)
        
        return counterexample
    
    def _extract_cvc5_counterexample(self, output: str) -> Dict[str, Any]:
        """Extract counterexample from CVC5 output."""
        
        counterexample = {}
        
        # Parse CVC5 model output
        lines = output.split('\n')
        for line in lines:
            # Look for variable assignments like "(define-fun x () Int 42)"
            match = re.search(r'\(define-fun\s+(\w+)\s+\(\)\s+\w+\s+([^)]+)\)', line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2).strip()
                
                # Try to convert to appropriate type
                try:
                    if var_value.isdigit() or (var_value.startswith('-') and var_value[1:].isdigit()):
                        counterexample[var_name] = int(var_value)
                    elif var_value.lower() in ['true', 'false']:
                        counterexample[var_name] = var_value.lower() == 'true'
                    else:
                        counterexample[var_name] = var_value
                except ValueError:
                    counterexample[var_name] = var_value
        
        return counterexample
    
    def decode_storage_slots(
        self,
        counterexample: Dict[str, Any],
        contract_path: Path
    ) -> Dict[str, Any]:
        """
        Decode storage slots into human-readable values.
        
        Args:
            counterexample: Raw counterexample from solver
            contract_path: Path to contract for storage layout
            
        Returns:
            Decoded counterexample with human-readable values
        """
        decoded = counterexample.copy()
        
        # Try to get storage layout from Slither
        try:
            cmd = ["slither", str(contract_path), "--print", "vars-and-auth", "--json", "-"]
            returncode, stdout, stderr = run_command(cmd)
            
            if returncode == 0:
                slither_output = json.loads(stdout)
                storage_layout = self._extract_storage_layout(slither_output)
                
                # Map storage slots to variable names
                for var_name, slot_info in storage_layout.items():
                    slot_key = f"storage_{slot_info['slot']}"
                    if slot_key in counterexample:
                        decoded[var_name] = counterexample[slot_key]
                        
        except Exception as e:
            logging.warning(f"Failed to decode storage slots: {e}")
        
        return decoded
    
    def _extract_storage_layout(self, slither_output: Dict) -> Dict[str, Dict]:
        """Extract storage layout from Slither output."""
        
        storage_layout = {}
        
        # Parse Slither storage information
        for result in slither_output.get("results", {}).get("detectors", []):
            for element in result.get("elements", []):
                if element.get("type") == "variable":
                    var_name = element.get("name")
                    if var_name and "slot" in element:
                        storage_layout[var_name] = {
                            "slot": element["slot"],
                            "type": element.get("type_string", "unknown")
                        }
        
        return storage_layout
