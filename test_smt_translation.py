#!/usr/bin/env python3
"""
Test the SMT translation functionality.
"""

from pathlib import Path
from property_gpt.utils import InvariantProperty
from property_gpt.prover import InvariantProver

def test_smt_translation():
    """Test SMT translation with various invariants."""
    
    print("🧪 Testing SMT Translation...")
    
    # Create prover
    prover = InvariantProver("z3", 30)
    
    # Test invariants
    test_invariants = [
        InvariantProperty(
            id="test_simple",
            property_type="postcondition",
            function_name="deposit",
            code="assert(owner == owner);",
            description="Simple tautology",
            confidence_score=0.9
        ),
        InvariantProperty(
            id="test_balance",
            property_type="postcondition", 
            function_name="deposit",
            code="assert(totalSupply >= 0);",
            description="Total supply non-negative",
            confidence_score=0.9
        ),
        InvariantProperty(
            id="test_complex",
            property_type="precondition",
            function_name="withdraw",
            code="require(balances[msg.sender] >= amount, \"Insufficient balance\");",
            description="Sufficient balance check",
            confidence_score=0.9
        )
    ]
    
    contract_path = Path("demo/Bank.sol")
    
    for i, invariant in enumerate(test_invariants, 1):
        print(f"\n--- Test {i}: {invariant.id} ---")
        print(f"Code: {invariant.code}")
        
        # Test SMT translation
        smt_content = prover._translate_to_smt(contract_path, invariant)
        
        if smt_content:
            print("✅ SMT translation successful")
            print("📄 SMT Content:")
            print("-" * 40)
            print(smt_content)
            print("-" * 40)
            
            # Test proving
            result = prover._prove_single_invariant(contract_path, invariant)
            print(f"🔍 Proof result: {result.proof_status}")
            if hasattr(result, 'proof_details'):
                print(f"📝 Details: {result.proof_details}")
        else:
            print("❌ SMT translation failed")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing SMT Translation")
    print("=" * 50)
    
    success = test_smt_translation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎯 SMT translation test completed")
    else:
        print("❌ SMT translation test failed")
