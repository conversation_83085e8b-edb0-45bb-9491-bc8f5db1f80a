#!/usr/bin/env python3
"""
Debug script to test invariant generation step by step.
"""

import os
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def test_generation_step_by_step():
    """Test each step of invariant generation."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a simple test function
        test_function = ContractFunction(
            name="withdraw",
            signature="withdraw(uint256)",
            visibility="external",
            state_mutability="nonpayable",
            parameters=[{"name": "amount", "type": "uint256"}],
            returns=[]
        )
        
        print("🧠 Step 1: Creating generation prompt...")
        prompt = generator._create_generation_prompt(test_function, [])
        print(f"✅ Prompt created (length: {len(prompt)})")
        print(f"📝 Prompt preview: {prompt[:300]}...")
        
        print("\n🤖 Step 2: Calling DeepSeek API...")
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=1000
        )
        
        generated_text = response.choices[0].message.content
        print(f"✅ DeepSeek response received (length: {len(generated_text)})")
        print(f"📝 Response: {generated_text}")
        
        print("\n🔍 Step 3: Parsing generated invariants...")
        invariants = generator._parse_generated_invariants(generated_text, test_function)
        print(f"✅ Parsed {len(invariants)} invariants:")
        
        for i, inv in enumerate(invariants):
            print(f"  {i+1}. {inv.property_type}: {inv.code}")
        
        if len(invariants) == 0:
            print("❌ No invariants parsed! Checking response format...")
            lines = generated_text.split('\n')
            for i, line in enumerate(lines):
                if any(keyword in line.upper() for keyword in ['PRECONDITION', 'POSTCONDITION', 'CROSS', 'REQUIRE', 'ASSERT']):
                    print(f"  Line {i}: {line}")
        
        return invariants
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    test_generation_step_by_step()
