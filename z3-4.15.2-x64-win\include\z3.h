/*++
Copyright (c) 2007 Microsoft Corporation

Module Name:

    z3.h

Abstract:

    Z3 API.

Author:

    <PERSON><PERSON> (nbjorner)
    <PERSON> (leonardo) 2007-06-8

Notes:

--*/

#pragma once

#include <stdbool.h>
#include <stdint.h>
#include "z3_macros.h"
#include "z3_api.h"
#include "z3_ast_containers.h"
#include "z3_algebraic.h"
#include "z3_polynomial.h"
#include "z3_rcf.h"
#include "z3_fixedpoint.h"
#include "z3_optimization.h"
#include "z3_fpa.h"
#include "z3_spacer.h"

