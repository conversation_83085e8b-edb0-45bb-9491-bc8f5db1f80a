#!/usr/bin/env python3
"""
Detailed debugging of the compilation step to find exactly why invariants fail.
"""

import os
import tempfile
from pathlib import Path
from property_gpt.utils import create_openai_client, ContractFunction, create_temp_file, run_command
from property_gpt.gen_props import InvariantGenerator

def debug_compilation_step_by_step():
    """Debug each step of the compilation process in detail."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a simple test function
        test_function = ContractFunction(
            name="withdraw",
            signature="withdraw(uint256)",
            visibility="external",
            state_mutability="nonpayable",
            parameters=[{"name": "amount", "type": "uint256"}],
            returns=[]
        )
        
        print("🧠 Step 1: Generate one invariant with DeepSeek...")
        
        # Generate just one invariant
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": generator._create_generation_prompt(test_function, [])}],
            temperature=0.2,
            max_tokens=300
        )
        
        generated_text = response.choices[0].message.content
        print(f"✅ Generated text: {generated_text[:200]}...")
        
        # Parse invariants
        invariants = generator._parse_generated_invariants(generated_text, test_function)
        print(f"✅ Parsed {len(invariants)} invariants")
        
        if not invariants:
            print("❌ No invariants parsed!")
            return
        
        # Test the first invariant
        inv = invariants[0]
        print(f"\n🔍 Testing invariant: {inv.property_type}")
        print(f"Code: {inv.code}")
        
        print("\n🧪 Step 2: Create test contract...")
        contract_code = generator._create_test_contract(inv, Path("demo/Bank.sol"))
        print(f"✅ Contract created (length: {len(contract_code)})")
        
        # Save the contract to see exactly what we're compiling
        with open("debug_contract.sol", "w") as f:
            f.write(contract_code)
        print("✅ Saved contract to debug_contract.sol")
        
        print("\n🔧 Step 3: Test compilation manually...")
        
        # Test compilation with detailed output
        cmd = ["solc", "debug_contract.sol"]
        returncode, stdout, stderr = run_command(cmd)
        
        print(f"Return code: {returncode}")
        print(f"Stdout: {stdout}")
        print(f"Stderr: {stderr}")
        
        if returncode == 0:
            print("✅ Manual compilation succeeded!")
        else:
            print("❌ Manual compilation failed!")
            print("Let's analyze the error...")
            
            # Common compilation issues
            if "SPDX-License-Identifier" in stderr:
                print("🔍 Issue: Duplicate SPDX license identifier")
            elif "pragma solidity" in stderr:
                print("🔍 Issue: Pragma version conflict")
            elif "ParserError" in stderr:
                print("🔍 Issue: Syntax error in generated code")
            elif "DeclarationError" in stderr:
                print("🔍 Issue: Variable/function declaration error")
            else:
                print("🔍 Issue: Unknown compilation error")
        
        print("\n🧪 Step 4: Test PropertyGPT's compilation method...")
        
        # Test exactly what PropertyGPT does
        compiled_inv = generator._compile_and_fix_invariant(inv, Path("demo/Bank.sol"), max_retries=1)
        
        if compiled_inv:
            print(f"✅ PropertyGPT compilation status: {compiled_inv.compilation_status}")
            if compiled_inv.compilation_status == "failed":
                print(f"❌ Error: {compiled_inv.compilation_error}")
        else:
            print("❌ PropertyGPT compilation returned None")
        
        print("\n🔍 Step 5: Examine the original Bank.sol...")
        with open("demo/Bank.sol", "r") as f:
            bank_content = f.read()
        
        print(f"Bank.sol preview:")
        print(bank_content[:300] + "...")
        
        # Check for potential issues
        if bank_content.count("SPDX-License-Identifier") > 1:
            print("⚠️ Multiple SPDX identifiers found in Bank.sol")
        if bank_content.count("pragma solidity") > 1:
            print("⚠️ Multiple pragma statements found in Bank.sol")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Detailed Compilation Debugging")
    print("=" * 50)
    
    success = debug_compilation_step_by_step()
    
    print("\n" + "=" * 50)
    if success:
        print("🎯 Debug completed - check the output above for issues")
    else:
        print("❌ Debug failed")
