#!/bin/bash

# PropertyGPT Demo Script
# This script demonstrates PropertyGPT's ability to find bugs in smart contracts

set -e  # Exit on any error

echo "🚀 PropertyGPT Demo - Smart Contract Bug Discovery"
echo "=================================================="
echo

# Check if we're in the right directory
if [ ! -f "demo/Bank.sol" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if PropertyGPT is installed
if ! command -v property-gpt &> /dev/null; then
    echo "❌ Error: property-gpt command not found. Please install PropertyGPT first:"
    echo "   pip install -e ."
    exit 1
fi

# Check for required environment variables
if [ -z "$OPENAI_API_KEY" ] && [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "❌ Error: No API key found"
    echo "   Please set either:"
    echo "   export OPENAI_API_KEY='your-openai-key-here'  # for OpenAI"
    echo "   export DEEPSEEK_API_KEY='your-deepseek-key-here'  # for DeepSeek (cheaper)"
    exit 1
fi

# Determine which API to use
if [ -n "$DEEPSEEK_API_KEY" ]; then
    API_PROVIDER="deepseek"
    API_MODEL="deepseek-chat"
    echo "🤖 Using DeepSeek API"
else
    API_PROVIDER="openai"
    API_MODEL="gpt-4o"
    echo "🤖 Using OpenAI API"
fi

echo "✅ Environment checks passed"
echo

# Step 1: Build knowledge base (if not already exists)
if [ ! -d "kb" ]; then
    echo "📚 Step 1: Building knowledge base from Certora specifications..."
    echo "This may take a few minutes as we process and embed the specification files..."
    
    property-gpt build-kb datasets/specs --output kb --api-provider $API_PROVIDER
    
    if [ $? -eq 0 ]; then
        echo "✅ Knowledge base built successfully!"
    else
        echo "❌ Failed to build knowledge base"
        exit 1
    fi
else
    echo "📚 Knowledge base already exists, skipping build step"
fi

echo

# Step 2: Analyze the vulnerable Bank contract
echo "🔍 Step 2: Analyzing Bank.sol for security vulnerabilities..."
echo "PropertyGPT will:"
echo "  1. Parse the contract with Slither"
echo "  2. Retrieve similar properties from the knowledge base"
echo "  3. Generate invariants using GPT-4o"
echo "  4. Compile and fix the generated invariants"
echo "  5. Prove/disprove invariants with Z3"
echo "  6. Generate a comprehensive report"
echo

# Run the analysis
property-gpt analyse demo/Bank.sol --output demo/bank_analysis.md --verbose \
  --api-provider $API_PROVIDER --model $API_MODEL

# Capture the exit code
ANALYSIS_EXIT_CODE=$?

echo
echo "📊 Analysis Results:"
echo "==================="

if [ $ANALYSIS_EXIT_CODE -eq 1 ]; then
    echo "🚨 PropertyGPT found potential security vulnerabilities!"
    echo
    echo "📄 Detailed report saved to: demo/bank_analysis.md"
    echo "📄 JSON summary saved to: demo/bank_analysis.json"
    echo
    echo "🔍 Key findings should include:"
    echo "  • Unchecked integer underflow in withdraw() function"
    echo "  • Missing balance validation"
    echo "  • Potential state inconsistency in emergencyWithdraw()"
    echo
    echo "✅ Demo completed successfully - bugs detected as expected!"
    
elif [ $ANALYSIS_EXIT_CODE -eq 0 ]; then
    echo "⚠️  No vulnerabilities detected (unexpected for this demo)"
    echo "📄 Report saved to: demo/bank_analysis.md"
    echo
    echo "This might indicate:"
    echo "  • The invariant generation didn't cover the vulnerable patterns"
    echo "  • The SMT solver couldn't find counterexamples"
    echo "  • The analysis parameters need adjustment"
    
else
    echo "❌ Analysis failed with exit code $ANALYSIS_EXIT_CODE"
    echo "Check the logs above for error details"
    exit 1
fi

echo
echo "🎯 Demo Summary:"
echo "==============="
echo "• Contract analyzed: demo/Bank.sol"
echo "• Knowledge base: $(find kb -name "*.jsonl" | wc -l) specification chunks"
echo "• Analysis time: Check the report for timing details"
echo "• Report location: demo/bank_analysis.md"
echo
echo "📖 Next steps:"
echo "  1. Review the generated report: demo/bank_analysis.md"
echo "  2. Examine the JSON summary: demo/bank_analysis.json"
echo "  3. Try analyzing your own contracts with: property-gpt analyse <contract.sol>"
echo "  4. Build custom knowledge bases with: property-gpt build-kb <spec-dir>"
echo
echo "🔗 For more information, see the README.md file"
echo
echo "✨ PropertyGPT demo completed!"
