<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Z3</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Z3.AlgebraicNum">
            <summary>
            Algebraic numbers
            </summary>
        </member>
        <member name="M:Microsoft.Z3.AlgebraicNum.ToUpper(System.UInt32)">
            <summary>
            Return a upper bound for a given real algebraic number. 
            The interval isolating the number is smaller than 1/10^<paramref name="precision"/>.    
            <seealso cref="P:Microsoft.Z3.Expr.IsAlgebraicNumber"/>   
            </summary>
            <param name="precision">the precision of the result</param>
            <returns>A numeral Expr of sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.AlgebraicNum.ToLower(System.UInt32)">
            <summary>
            Return a lower bound for the given real algebraic number. 
            The interval isolating the number is smaller than 1/10^<paramref name="precision"/>.    
            <seealso cref="P:Microsoft.Z3.Expr.IsAlgebraicNumber"/>
            </summary>
            <param name="precision"></param>
            <returns>A numeral Expr of sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.AlgebraicNum.ToDecimal(System.UInt32)">
            <summary>
            Returns a string representation in decimal notation.
            </summary>
            <remarks>The result has at most <paramref name="precision"/> decimal places.</remarks>    
        </member>
        <member name="T:Microsoft.Z3.ApplyResult">
            <summary>
            ApplyResult objects represent the result of an application of a 
            tactic to a goal. It contains the subgoals that were produced.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ApplyResult.NumSubgoals">
            <summary>
            The number of Subgoals.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ApplyResult.Subgoals">
            <summary>
            Retrieves the subgoals from the ApplyResult.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ApplyResult.ToString">
            <summary>
            A string representation of the ApplyResult.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ArithExpr">
            <summary>
            Arithmetic expressions (int/real)
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for ArithExpr </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Division(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical division operator (over reals) </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Division(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Division(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Division(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Division(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_UnaryNegation(Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Subtraction(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Subtraction(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Subtraction(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Subtraction(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Subtraction(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Addition(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Addition(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Addition(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Addition(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Addition(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Multiply(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Multiply(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Multiply(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Multiply(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_Multiply(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThanOrEqual(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThanOrEqual(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThanOrEqual(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThanOrEqual(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThanOrEqual(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThan(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThan(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThan(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThan(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_LessThan(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThan(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThan(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThan(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThan(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThan(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThanOrEqual(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThanOrEqual(Microsoft.Z3.ArithExpr,System.Int32)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThanOrEqual(Microsoft.Z3.ArithExpr,System.Double)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThanOrEqual(System.Int32,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="M:Microsoft.Z3.ArithExpr.op_GreaterThanOrEqual(System.Double,Microsoft.Z3.ArithExpr)">
            <summary> Operator overloading for arithmetical operator </summary>
        </member>
        <member name="T:Microsoft.Z3.ArithSort">
            <summary>
            An arithmetic sort, i.e., Int or Real.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ArrayExpr">
            <summary>
            Array expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ArrayExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for ArrayExpr </summary>
        </member>
        <member name="P:Microsoft.Z3.ArrayExpr.Item(Microsoft.Z3.Expr)">
            <summary> Single argument select </summary>
        </member>
        <member name="P:Microsoft.Z3.ArrayExpr.Item(System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr})">
            <summary> Multi argument select </summary>
        </member>
        <member name="T:Microsoft.Z3.ArraySort">
            <summary>
            Array sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ArraySort.Domain">
            <summary>
            The domain of the array sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ArraySort.Range">
            <summary>
            The range of the array sort.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.AST">
            <summary>
            The abstract syntax tree (AST) class.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.AST.op_Equality(Microsoft.Z3.AST,Microsoft.Z3.AST)">
            <summary>
            Comparison operator.
            </summary>
            <param name="a">An AST</param>
            <param name="b">An AST</param>
            <returns>True if <paramref name="a"/> and <paramref name="b"/> are from the same context
            and represent the same sort; false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.AST.op_Inequality(Microsoft.Z3.AST,Microsoft.Z3.AST)">
            <summary>
            Comparison operator.
            </summary>
            <param name="a">An AST</param>
            <param name="b">An AST</param>
            <returns>True if <paramref name="a"/> and <paramref name="b"/> are not from the same context
            or represent different sorts; false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.AST.Equals(System.Object)">
            <summary>
            Object comparison.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.AST.CompareTo(System.Object)">
            <summary>
            Object Comparison.
            </summary>
            <param name="other">Another AST</param>
            <returns>Negative if the object should be sorted before <paramref name="other"/>, positive if after else zero.</returns>
        </member>
        <member name="M:Microsoft.Z3.AST.GetHashCode">
            <summary>
            The AST's hash code.
            </summary>
            <returns>A hash code</returns>
        </member>
        <member name="P:Microsoft.Z3.AST.Id">
            <summary>
            A unique identifier for the AST (unique among all ASTs).
            </summary>
        </member>
        <member name="M:Microsoft.Z3.AST.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the AST to the Context <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A copy of the AST which is associated with <paramref name="ctx"/></returns>
        </member>
        <member name="P:Microsoft.Z3.AST.ASTKind">
            <summary>
            The kind of the AST.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.AST.IsExpr">
            <summary>
            Indicates whether the AST is an Expr
            </summary>
        </member>
        <member name="P:Microsoft.Z3.AST.IsApp">
            <summary>
            Indicates whether the AST is an application
            </summary>
        </member>
        <member name="P:Microsoft.Z3.AST.IsVar">
            <summary>
            Indicates whether the AST is a BoundVariable
            </summary>
        </member>
        <member name="P:Microsoft.Z3.AST.IsQuantifier">
            <summary>
            Indicates whether the AST is a Quantifier
            </summary>
        </member>
        <member name="P:Microsoft.Z3.AST.IsSort">
            <summary>
            Indicates whether the AST is a Sort
            </summary>
        </member>
        <member name="P:Microsoft.Z3.AST.IsFuncDecl">
            <summary>
            Indicates whether the AST is a FunctionDeclaration
            </summary>
        </member>
        <member name="M:Microsoft.Z3.AST.ToString">
            <summary>
            A string representation of the AST.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.AST.SExpr">
            <summary>
            A string representation of the AST in s-expression notation.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ASTMap">
            <summary>
            Map from AST to AST
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ASTMap.Contains(Microsoft.Z3.AST)">
            <summary>
            Checks whether the map contains the key <paramref name="k"/>.
            </summary>
            <param name="k">An AST</param>
            <returns>True if <paramref name="k"/> is a key in the map, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.ASTMap.Find(Microsoft.Z3.AST)">
            <summary>
            Finds the value associated with the key <paramref name="k"/>.
            </summary>
            <remarks>
            This function signs an error when <paramref name="k"/> is not a key in the map.
            </remarks>
            <param name="k">An AST</param>    
        </member>
        <member name="M:Microsoft.Z3.ASTMap.Insert(Microsoft.Z3.AST,Microsoft.Z3.AST)">
            <summary>
            Stores or replaces a new key/value pair in the map.
            </summary>
            <param name="k">The key AST</param>
            <param name="v">The value AST</param>
        </member>
        <member name="M:Microsoft.Z3.ASTMap.Erase(Microsoft.Z3.AST)">
            <summary>
            Erases the key <paramref name="k"/> from the map.
            </summary>
            <param name="k">An AST</param>
        </member>
        <member name="M:Microsoft.Z3.ASTMap.Reset">
            <summary>
            Removes all keys from the map.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ASTMap.Size">
            <summary>
            The size of the map
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ASTMap.Keys">
            <summary>
            The keys stored in the map.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ASTMap.ToString">
            <summary>
            Retrieves a string representation of the map. 
            </summary>    
        </member>
        <member name="T:Microsoft.Z3.ASTVector">
            <summary>
            Vectors of ASTs.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ASTVector.Size">
            <summary>
            The size of the vector
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ASTVector.Item(System.UInt32)">
            <summary>
            Retrieves the i-th object in the vector.
            </summary>
            <remarks>May throw an IndexOutOfBoundsException when <paramref name="i"/> is out of range.</remarks>
            <param name="i">Index</param>
            <returns>An AST</returns>
        </member>
        <member name="M:Microsoft.Z3.ASTVector.Resize(System.UInt32)">
            <summary>
            Resize the vector to <paramref name="newSize"/>.
            </summary>
            <param name="newSize">The new size of the vector.</param>
        </member>
        <member name="M:Microsoft.Z3.ASTVector.Push(Microsoft.Z3.AST)">
            <summary>
            Add the AST <paramref name="a"/> to the back of the vector. The size
            is increased by 1.
            </summary>
            <param name="a">An AST</param>
        </member>
        <member name="M:Microsoft.Z3.ASTVector.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates all ASTs in the vector to <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A new ASTVector</returns>
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToString">
            <summary>
            Retrieves a string representation of the vector. 
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToArray">
            <summary>
            Translates an AST vector into an AST[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToExprArray">
            <summary>
            Translates an ASTVector into an Expr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToBoolExprArray">
            <summary>
            Translates an ASTVector into a BoolExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToBitVecExprArray">
            <summary>
            Translates an ASTVector into a BitVecExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToArithExprArray">
            <summary>
            Translates an ASTVector into a ArithExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToArrayExprArray">
            <summary>
            Translates an ASTVector into a ArrayExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToDatatypeExprArray">
            <summary>
            Translates an ASTVector into a DatatypeExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToFPExprArray">
            <summary>
            Translates an ASTVector into a FPExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToFPRMExprArray">
            <summary>
            Translates an ASTVector into a FPRMExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToIntExprArray">
            <summary>
            Translates an ASTVector into a IntExpr[]
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.ASTVector.ToRealExprArray">
            <summary>
            Translates an ASTVector into a RealExpr[]
            </summary>    
        </member>
        <member name="T:Microsoft.Z3.BitVecExpr">
            <summary>
            Bit-vector expressions
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecExpr.SortSize">
            <summary>
            The size of the sort of a bit-vector term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.BitVecExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for BitVecExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.BitVecNum">
            <summary>
            Bit-vector numerals
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecNum.UInt64">
            <summary>
            Retrieve the 64-bit unsigned integer value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecNum.Int">
            <summary>
            Retrieve the int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecNum.Int64">
            <summary>
            Retrieve the 64-bit int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecNum.UInt">
            <summary>
            Retrieve the int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecNum.BigInteger">
            <summary>
            Retrieve the BigInteger value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.BitVecNum.ToString">
            <summary>
            Returns a decimal string representation of the numeral.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.BitVecNum.ToBinaryString">
            <summary>
            Returns a binary string representation of the numeral.
            </summary>        
        </member>
        <member name="T:Microsoft.Z3.BitVecSort">
            <summary>
            Bit-vector sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.BitVecSort.Size">
            <summary>
            The size of the bit-vector sort.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.BoolExpr">
            <summary>
            Boolean expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.BoolExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for BoolExpr </summary>
        </member>
        <member name="M:Microsoft.Z3.BoolExpr.op_BitwiseOr(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary> Disjunction of Boolean expressions </summary>
        </member>
        <member name="M:Microsoft.Z3.BoolExpr.op_BitwiseAnd(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary> Conjunction of Boolean expressions </summary>
        </member>
        <member name="M:Microsoft.Z3.BoolExpr.op_ExclusiveOr(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary> Xor of Boolean expressions </summary>
        </member>
        <member name="M:Microsoft.Z3.BoolExpr.op_LogicalNot(Microsoft.Z3.BoolExpr)">
            <summary> Negation </summary>
        </member>
        <member name="T:Microsoft.Z3.BoolSort">
            <summary>
            A Boolean sort.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.CharSort">
            <summary>
             A Character sort
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Constructor">
            <summary>
            Constructors are used for datatype sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Constructor.NumFields">
            <summary>
            The number of fields of the constructor.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Constructor.ConstructorDecl">
            <summary>
            The function declaration of the constructor.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Constructor.TesterDecl">
            <summary>
            The function declaration of the tester.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Constructor.AccessorDecls">
            <summary>
            The function declarations of the accessors
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Constructor.Finalize">
            <summary>
            Destructor.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ConstructorList">
            <summary>
            Lists of constructors
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ConstructorList.Finalize">
            <summary>
            Destructor.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Context">
            <summary>
            The main interaction with Z3 happens via the Context.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.#ctor(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Constructor.
            </summary>
            <remarks>
            The following parameters can be set:
                - proof  (Boolean)           Enable proof generation
                - debug_ref_count (Boolean)  Enable debug support for Z3_ast reference counting
                - trace  (Boolean)           Tracing support for VCC
                - trace_file_name (String)   Trace out file for VCC traces
                - timeout (unsigned)         default timeout (in milliseconds) used for solvers
                - well_sorted_check          type checker
                - auto_config                use heuristics to automatically select solver and configure it
                - model                      model generation for solvers, this parameter can be overwritten when creating a solver
                - model_validate             validate models produced by solvers
                - unsat_core                 unsat-core generation for solvers, this parameter can be overwritten when creating a solver
            Note that in previous versions of Z3, this constructor was also used to set global and module parameters.
            For this purpose we should now use <see cref="M:Microsoft.Z3.Global.SetParameter(System.String,System.String)"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.#ctor(System.IntPtr)">
            <summary>
            Internal Constructor. It is used from UserPropagator
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSymbol(System.Int32)">
            <summary>
            Creates a new symbol using an integer.
            </summary>
            <remarks>
            Not all integers can be passed to this function.
            The legal range of unsigned integers is 0 to 2^30-1.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSymbol(System.String)">
            <summary>
            Create a symbol using a string.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSymbols(System.String[])">
            <summary>
            Create an array of symbols.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.BoolSort">
            <summary>
            Retrieves the Boolean sort of the context.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.IntSort">
            <summary>
            Retrieves the Integer sort of the context.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.RealSort">
            <summary>
            Retrieves the Real sort of the context.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.CharSort">
            <summary>
            Retrieves the String sort of the context.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.StringSort">
            <summary>
            Retrieves the String sort of the context.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBoolSort">
            <summary>
            Create a new Boolean sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUninterpretedSort(Microsoft.Z3.Symbol)">
            <summary>
            Create a new uninterpreted sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUninterpretedSort(System.String)">
            <summary>
            Create a new uninterpreted sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIntSort">
            <summary>
            Create a new integer sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRealSort">
            <summary>
            Create a real sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBitVecSort(System.UInt32)">
            <summary>
            Create a new bit-vector sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSeqSort(Microsoft.Z3.Sort)">
            <summary>
            Create a new sequence sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReSort(Microsoft.Z3.SeqSort)">
            <summary>
            Create a new regular expression sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Create a new array sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort[],Microsoft.Z3.Sort)">
            <summary>
            Create a new n-ary array sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkTupleSort(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol[],Microsoft.Z3.Sort[])">
            <summary>
            Create a new tuple sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkEnumSort(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol[])">
            <summary>
            Create a new enumeration sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkEnumSort(System.String,System.String[])">
            <summary>
            Create a new enumeration sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkListSort(Microsoft.Z3.Symbol,Microsoft.Z3.Sort)">
            <summary>
            Create a new list sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkListSort(System.String,Microsoft.Z3.Sort)">
            <summary>
            Create a new list sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFiniteDomainSort(Microsoft.Z3.Symbol,System.UInt64)">
            <summary>
            Create a new finite domain sort.
            <returns>The result is a sort</returns>
            </summary>
            <param name="name">The name used to identify the sort</param>
            <param name="size">The size of the sort</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFiniteDomainSort(System.String,System.UInt64)">
            <summary>
            Create a new finite domain sort.
            <returns>The result is a sort</returns>
            Elements of the sort are created using <seealso cref="M:Microsoft.Z3.Context.MkNumeral(System.UInt64,Microsoft.Z3.Sort)"/>,
            and the elements range from 0 to <tt>size-1</tt>.
            </summary>
            <param name="name">The name used to identify the sort</param>
            <param name="size">The size of the sort</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConstructor(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol,Microsoft.Z3.Symbol[],Microsoft.Z3.Sort[],System.UInt32[])">
            <summary>
            Create a datatype constructor.
            </summary>
            <param name="name">constructor name</param>
            <param name="recognizer">name of recognizer function.</param>
            <param name="fieldNames">names of the constructor fields.</param>
            <param name="sorts">field sorts, 0 if the field sort refers to a recursive sort.</param>
            <param name="sortRefs">reference to datatype sort that is an argument to the constructor;
            if the corresponding sort reference is 0, then the value in sort_refs should be an index
            referring to one of the recursive datatypes that is declared.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConstructor(System.String,System.String,System.String[],Microsoft.Z3.Sort[],System.UInt32[])">
            <summary>
            Create a datatype constructor.
            </summary>
            <param name="name"></param>
            <param name="recognizer"></param>
            <param name="fieldNames"></param>
            <param name="sorts"></param>
            <param name="sortRefs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDatatypeSort(Microsoft.Z3.Symbol,Microsoft.Z3.Constructor[])">
            <summary>
            Create a new datatype sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDatatypeSort(System.String,Microsoft.Z3.Constructor[])">
            <summary>
            Create a new datatype sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDatatypeSorts(Microsoft.Z3.Symbol[],Microsoft.Z3.Constructor[][])">
            <summary>
            Create mutually recursive datatypes.
            </summary>
            <param name="names">names of datatype sorts</param>
            <param name="c">list of constructors, one list per sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDatatypeSorts(System.String[],Microsoft.Z3.Constructor[][])">
            <summary>
             Create mutually recursive data-types.
            </summary>
            <param name="names"></param>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUpdateField(Microsoft.Z3.FuncDecl,Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
            Update a datatype field at expression t with value v.
            The function performs a record update at t. The field
            that is passed in as argument is updated with value v,
            the remaining fields of t are unchanged.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFuncDecl(Microsoft.Z3.Symbol,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)">
            <summary>
            Creates a new function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFuncDecl(Microsoft.Z3.Symbol,Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Creates a new function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFuncDecl(System.String,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)">
            <summary>
            Creates a new function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRecFuncDecl(System.String,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)">
            <summary>
            Creates a new recursive function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.AddRecDef(Microsoft.Z3.FuncDecl,Microsoft.Z3.Expr[],Microsoft.Z3.Expr)">
            <summary>
            Bind a definition to a recursive function declaration.
            The function must have previously been created using
            MkRecFuncDecl. The body may contain recursive uses of the function or
            other mutually recursive functions. 
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFuncDecl(System.String,Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Creates a new function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFreshFuncDecl(System.String,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)">
            <summary>
            Creates a fresh function declaration with a name prefixed with <paramref name="prefix"/>.
            </summary>
            <seealso cref="M:Microsoft.Z3.Context.MkFuncDecl(System.String,Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkFuncDecl(System.String,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConstDecl(Microsoft.Z3.Symbol,Microsoft.Z3.Sort)">
            <summary>
            Creates a new constant function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConstDecl(System.String,Microsoft.Z3.Sort)">
            <summary>
            Creates a new constant function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFreshConstDecl(System.String,Microsoft.Z3.Sort)">
            <summary>
            Creates a fresh constant function declaration with a name prefixed with <paramref name="prefix"/>.
            </summary>
            <seealso cref="M:Microsoft.Z3.Context.MkFuncDecl(System.String,Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkFuncDecl(System.String,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUserPropagatorFuncDecl(System.String,Microsoft.Z3.Sort[],Microsoft.Z3.Sort)">
            <summary>
            Declare a function to be processed by the user propagator plugin.
            </summary>               
        </member>
        <member name="M:Microsoft.Z3.Context.MkBound(System.UInt32,Microsoft.Z3.Sort)">
            <summary>
            Creates a new bound variable.
            </summary>
            <param name="index">The de-Bruijn index of the variable</param>
            <param name="ty">The sort of the variable</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPattern(Microsoft.Z3.Expr[])">
            <summary>
            Create a quantifier pattern.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConst(Microsoft.Z3.Symbol,Microsoft.Z3.Sort)">
            <summary>
            Creates a new Constant of sort <paramref name="range"/> and named <paramref name="name"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConst(System.String,Microsoft.Z3.Sort)">
            <summary>
            Creates a new Constant of sort <paramref name="range"/> and named <paramref name="name"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFreshConst(System.String,Microsoft.Z3.Sort)">
            <summary>
            Creates a fresh Constant of sort <paramref name="range"/> and a
            name prefixed with <paramref name="prefix"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConst(Microsoft.Z3.FuncDecl)">
            <summary>
            Creates a fresh constant from the FuncDecl <paramref name="f"/>.
            </summary>
            <param name="f">A decl of a 0-arity function</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBoolConst(Microsoft.Z3.Symbol)">
            <summary>
            Create a Boolean constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBoolConst(System.String)">
            <summary>
            Create a Boolean constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIntConst(Microsoft.Z3.Symbol)">
            <summary>
            Creates an integer constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIntConst(System.String)">
            <summary>
            Creates an integer constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRealConst(Microsoft.Z3.Symbol)">
            <summary>
            Creates a real constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRealConst(System.String)">
            <summary>
            Creates a real constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVConst(Microsoft.Z3.Symbol,System.UInt32)">
            <summary>
            Creates a bit-vector constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVConst(System.String,System.UInt32)">
            <summary>
            Creates a bit-vector constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkApp(Microsoft.Z3.FuncDecl,Microsoft.Z3.Expr[])">
            <summary>
            Create a new function application.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkApp(Microsoft.Z3.FuncDecl,System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr})">
            <summary>
            Create a new function application.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkTrue">
            <summary>
            The true Term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFalse">
            <summary>
            The false Term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBool(System.Boolean)">
            <summary>
            Creates a Boolean value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkEq(Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
            Creates the equality <paramref name="x"/> = <paramref name="y"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDistinct(Microsoft.Z3.Expr[])">
            <summary>
            Creates a <c>distinct</c> term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDistinct(System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr})">
            <summary>
            Creates a <c>distinct</c> term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNot(Microsoft.Z3.BoolExpr)">
            <summary>
             Mk an expression representing <c>not(a)</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkITE(Microsoft.Z3.BoolExpr,Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
             Create an expression representing an if-then-else: <c>ite(t1, t2, t3)</c>.
            </summary>
            <param name="t1">An expression with Boolean sort</param>
            <param name="t2">An expression </param>
            <param name="t3">An expression with the same sort as <paramref name="t2"/></param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIff(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary>
            Create an expression representing <c>t1 iff t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkImplies(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary>
            Create an expression representing <c>t1 -> t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkXor(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary>
            Create an expression representing <c>t1 xor t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkXor(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Create an expression representing <c>t1 xor t2 xor t3 ... </c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAnd(Microsoft.Z3.BoolExpr[])">
            <summary>
            Create an expression representing <c>t[0] and t[1] and ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAnd(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Create an expression representing <c>t[0] and t[1] and ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkOr(Microsoft.Z3.BoolExpr[])">
            <summary>
            Create an expression representing <c>t[0] or t[1] or ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkOr(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Create an expression representing <c>t[0] or t[1] or ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAdd(Microsoft.Z3.ArithExpr[])">
            <summary>
            Create an expression representing <c>t[0] + t[1] + ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAdd(System.Collections.Generic.IEnumerable{Microsoft.Z3.ArithExpr})">
            <summary>
            Create an expression representing <c>t[0] + t[1] + ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkMul(Microsoft.Z3.ArithExpr[])">
            <summary>
            Create an expression representing <c>t[0] * t[1] * ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkMul(System.Collections.Generic.IEnumerable{Microsoft.Z3.ArithExpr})">
            <summary>
            Create an expression representing <c>t[0] * t[1] * ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSub(Microsoft.Z3.ArithExpr[])">
            <summary>
            Create an expression representing <c>t[0] - t[1] - ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUnaryMinus(Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>-t</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDiv(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>t1 / t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkMod(Microsoft.Z3.IntExpr,Microsoft.Z3.IntExpr)">
            <summary>
            Create an expression representing <c>t1 mod t2</c>.
            </summary>
            <remarks>The arguments must have int type.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRem(Microsoft.Z3.IntExpr,Microsoft.Z3.IntExpr)">
            <summary>
            Create an expression representing <c>t1 rem t2</c>.
            </summary>
            <remarks>The arguments must have int type.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPower(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>t1 ^ t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkLt(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>t1 &lt; t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkLe(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>t1 &lt;= t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkGt(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>t1 &gt; t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkGe(Microsoft.Z3.ArithExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Create an expression representing <c>t1 &gt;= t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt2Real(Microsoft.Z3.IntExpr)">
             <summary>
             Coerce an integer to a real.
             </summary>
             <remarks>
             There is also a converse operation exposed. It follows the semantics prescribed by the SMT-LIB standard.
            
             You can take the floor of a real by creating an auxiliary integer Term <c>k</c> and
             and asserting <c>MakeInt2Real(k) &lt;= t1 &lt; MkInt2Real(k)+1</c>.
             The argument must be of integer sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal2Int(Microsoft.Z3.RealExpr)">
            <summary>
            Coerce a real to an integer.
            </summary>
            <remarks>
            The semantics of this function follows the SMT-LIB standard for the function to_int.
            The argument must be of real sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIsInteger(Microsoft.Z3.RealExpr)">
            <summary>
            Creates an expression that checks whether a real number is an integer.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVNot(Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise negation.
            </summary>
            <remarks>The argument must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVRedAND(Microsoft.Z3.BitVecExpr)">
            <summary>
            Take conjunction of bits in a vector, return vector of length 1.
            </summary>
            <remarks>The argument must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVRedOR(Microsoft.Z3.BitVecExpr)">
            <summary>
            Take disjunction of bits in a vector, return vector of length 1.
            </summary>
            <remarks>The argument must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVAND(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise conjunction.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVOR(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise disjunction.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVXOR(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise XOR.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVNAND(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise NAND.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVNOR(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise NOR.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVXNOR(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bitwise XNOR.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVNeg(Microsoft.Z3.BitVecExpr)">
            <summary>
            Standard two's complement unary minus.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVAdd(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement addition.
            </summary>
            <remarks>The arguments must have the same bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSub(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement subtraction.
            </summary>
            <remarks>The arguments must have the same bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVMul(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement multiplication.
            </summary>
            <remarks>The arguments must have the same bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVUDiv(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Unsigned division.
            </summary>
            <remarks>
            It is defined as the floor of <c>t1/t2</c> if \c t2 is
            different from zero. If <c>t2</c> is zero, then the result
            is undefined.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSDiv(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
             <summary>
             Signed division.
             </summary>
             <remarks>
             It is defined in the following way:
            
             - The \c floor of <c>t1/t2</c> if \c t2 is different from zero, and <c>t1*t2 >= 0</c>.
            
             - The \c ceiling of <c>t1/t2</c> if \c t2 is different from zero, and <c>t1*t2 &lt; 0</c>.
            
             If <c>t2</c> is zero, then the result is undefined.
             The arguments must have the same bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVURem(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Unsigned remainder.
            </summary>
            <remarks>
            It is defined as <c>t1 - (t1 /u t2) * t2</c>, where <c>/u</c> represents unsigned division.
            If <c>t2</c> is zero, then the result is undefined.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSRem(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
             <summary>
             Signed remainder.
             </summary>
             <remarks>
             It is defined as <c>t1 - (t1 /s t2) * t2</c>, where <c>/s</c> represents signed division.
             The most significant bit (sign) of the result is equal to the most significant bit of \c t1.
            
             If <c>t2</c> is zero, then the result is undefined.
             The arguments must have the same bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSMod(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement signed remainder (sign follows divisor).
            </summary>
            <remarks>
            If <c>t2</c> is zero, then the result is undefined.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVULT(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Unsigned less-than
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSLT(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement signed less-than
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVULE(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Unsigned less-than or equal to.
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSLE(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement signed less-than or equal to.
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVUGE(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Unsigned greater than or equal to.
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSGE(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
             Two's complement signed greater than or equal to.
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVUGT(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Unsigned greater-than.
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSGT(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Two's complement signed greater-than.
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConcat(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bit-vector concatenation.
            </summary>
            <remarks>
            The arguments must have a bit-vector sort.
            </remarks>
            <returns>
            The result is a bit-vector of size <c>n1+n2</c>, where <c>n1</c> (<c>n2</c>)
            is the size of <c>t1</c> (<c>t2</c>).
            </returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkExtract(System.UInt32,System.UInt32,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bit-vector extraction.
            </summary>
            <remarks>
            Extract the bits <paramref name="high"/> down to <paramref name="low"/> from a bitvector of
            size <c>m</c> to yield a new bitvector of size <c>n</c>, where
            <c>n = high - low + 1</c>.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSignExt(System.UInt32,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bit-vector sign extension.
            </summary>
            <remarks>
            Sign-extends the given bit-vector to the (signed) equivalent bitvector of
            size <c>m+i</c>, where \c m is the size of the given bit-vector.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkZeroExt(System.UInt32,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bit-vector zero extension.
            </summary>
            <remarks>
            Extend the given bit-vector with zeros to the (unsigned) equivalent
            bitvector of size <c>m+i</c>, where \c m is the size of the
            given bit-vector.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRepeat(System.UInt32,Microsoft.Z3.BitVecExpr)">
            <summary>
            Bit-vector repetition.
            </summary>
            <remarks>
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSHL(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
             <summary>
             Shift left.
             </summary>
             <remarks>
             It is equivalent to multiplication by <c>2^x</c> where \c x is the value of <paramref name="t2"/>.
            
             NB. The semantics of shift operations varies between environments. This
             definition does not necessarily capture directly the semantics of the
             programming language or assembly architecture you are modeling.
            
             The arguments must have a bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVLSHR(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
             <summary>
             Logical shift right
             </summary>
             <remarks>
             It is equivalent to unsigned division by <c>2^x</c> where \c x is the value of <paramref name="t2"/>.
            
             NB. The semantics of shift operations varies between environments. This
             definition does not necessarily capture directly the semantics of the
             programming language or assembly architecture you are modeling.
            
             The arguments must have a bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVASHR(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
             <summary>
             Arithmetic shift right
             </summary>
             <remarks>
             It is like logical shift right except that the most significant
             bits of the result always copy the most significant bit of the
             second argument.
            
             NB. The semantics of shift operations varies between environments. This
             definition does not necessarily capture directly the semantics of the
             programming language or assembly architecture you are modeling.
            
             The arguments must have a bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVRotateLeft(System.UInt32,Microsoft.Z3.BitVecExpr)">
            <summary>
            Rotate Left.
            </summary>
            <remarks>
            Rotate bits of \c t to the left \c i times.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVRotateRight(System.UInt32,Microsoft.Z3.BitVecExpr)">
            <summary>
            Rotate Right.
            </summary>
            <remarks>
            Rotate bits of \c t to the right \c i times.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVRotateLeft(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Rotate Left.
            </summary>
            <remarks>
            Rotate bits of <paramref name="t1"/> to the left <paramref name="t2"/> times.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVRotateRight(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Rotate Right.
            </summary>
            <remarks>
            Rotate bits of <paramref name="t1"/> to the right<paramref name="t2"/> times.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt2BV(System.UInt32,Microsoft.Z3.IntExpr)">
             <summary>
             Create an <paramref name="n"/> bit bit-vector from the integer argument <paramref name="t"/>.
             </summary>
             <remarks>
             NB. This function is essentially treated as uninterpreted.
             So you cannot expect Z3 to precisely reflect the semantics of this function
             when solving constraints with this function.
            
             The argument must be of integer sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV2Int(Microsoft.Z3.BitVecExpr,System.Boolean)">
             <summary>
             Create an integer from the bit-vector argument <paramref name="t"/>.
             </summary>
             <remarks>
             If \c is_signed is false, then the bit-vector \c t1 is treated as unsigned.
             So the result is non-negative and in the range <c>[0..2^N-1]</c>, where
             N are the number of bits in <paramref name="t"/>.
             If \c is_signed is true, \c t1 is treated as a signed bit-vector.
            
             NB. This function is essentially treated as uninterpreted.
             So you cannot expect Z3 to precisely reflect the semantics of this function
             when solving constraints with this function.
            
             The argument must be of bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVAddNoOverflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr,System.Boolean)">
            <summary>
            Create a predicate that checks that the bit-wise addition does not overflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVAddNoUnderflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Create a predicate that checks that the bit-wise addition does not underflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSubNoOverflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Create a predicate that checks that the bit-wise subtraction does not overflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSubNoUnderflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr,System.Boolean)">
            <summary>
            Create a predicate that checks that the bit-wise subtraction does not underflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVSDivNoOverflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Create a predicate that checks that the bit-wise signed division does not overflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVNegNoOverflow(Microsoft.Z3.BitVecExpr)">
            <summary>
            Create a predicate that checks that the bit-wise negation does not overflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVMulNoOverflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr,System.Boolean)">
            <summary>
            Create a predicate that checks that the bit-wise multiplication does not overflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBVMulNoUnderflow(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Create a predicate that checks that the bit-wise multiplication does not underflow.
            </summary>
            <remarks>
            The arguments must be of bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkArrayConst(Microsoft.Z3.Symbol,Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Create an array constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkArrayConst(System.String,Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Create an array constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)">
             <summary>
             Array read.
             </summary>
             <remarks>
             The argument <c>a</c> is the array and <c>i</c> is the index
             of the array that gets read.
            
             The node <c>a</c> must have an array sort <c>[domain -> range]</c>,
             and <c>i</c> must have the sort <c>domain</c>.
             The sort of the result is <c>range</c>.
             <seealso cref="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
             <seealso cref="M:Microsoft.Z3.Context.MkStore(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr,Microsoft.Z3.Expr)"/>
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr[])">
             <summary>
             Array read.
             </summary>
             <remarks>
             The argument <c>a</c> is the array and <c>args</c> are the indices
             of the array that gets read.
            
             The node <c>a</c> must have an array sort <c>[domain1,..,domaink -> range]</c>,
             and <c>args</c> must have the sort <c>domain1,..,domaink</c>.
             The sort of the result is <c>range</c>.
             <seealso cref="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
             <seealso cref="M:Microsoft.Z3.Context.MkStore(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr,Microsoft.Z3.Expr)"/>
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkStore(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
            Array update.
            </summary>
            <remarks>
            The node <c>a</c> must have an array sort <c>[domain -> range]</c>,
            <c>i</c> must have sort <c>domain</c>,
            <c>v</c> must have sort range. The sort of the result is <c>[domain -> range]</c>.
            The semantics of this function is given by the theory of arrays described in the SMT-LIB
            standard. See http://smtlib.org for more details.
            The result of this function is an array that is equal to <c>a</c>
            (with respect to <c>select</c>)
            on all indices except for <c>i</c>, where it maps to <c>v</c>
            (and the <c>select</c> of <c>a</c> with
            respect to <c>i</c> may be a different value).
            <seealso cref="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr[])"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkStore(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr[],Microsoft.Z3.Expr)">
            <summary>
            Array update.
            </summary>
            <remarks>
            The node <c>a</c> must have an array sort <c>[domain1,..,domaink -> range]</c>,
            <c>args</c> must have sort <c>domain1,..,domaink</c>,
            <c>v</c> must have sort range. The sort of the result is <c>[domain -> range]</c>.
            The semantics of this function is given by the theory of arrays described in the SMT-LIB
            standard. See http://smtlib.org for more details.
            The result of this function is an array that is equal to <c>a</c>
            (with respect to <c>select</c>)
            on all indices except for <c>args</c>, where it maps to <c>v</c>
            (and the <c>select</c> of <c>a</c> with
            respect to <c>args</c> may be a different value).
            <seealso cref="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr[])"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConstArray(Microsoft.Z3.Sort,Microsoft.Z3.Expr)">
            <summary>
            Create a constant array.
            </summary>
            <remarks>
            The resulting term is an array, such that a <c>select</c>on an arbitrary index
            produces the value <c>v</c>.
            <seealso cref="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkMap(Microsoft.Z3.FuncDecl,Microsoft.Z3.ArrayExpr[])">
            <summary>
            Maps f on the argument arrays.
            </summary>
            <remarks>
            Each element of <c>args</c> must be of an array sort <c>[domain_i -> range_i]</c>.
            The function declaration <c>f</c> must have type <c> range_1 .. range_n -> range</c>.
            <c>v</c> must have sort range. The sort of the result is <c>[domain_i -> range]</c>.
            <seealso cref="M:Microsoft.Z3.Context.MkArraySort(Microsoft.Z3.Sort,Microsoft.Z3.Sort)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkSelect(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)"/>
            <seealso cref="M:Microsoft.Z3.Context.MkStore(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr,Microsoft.Z3.Expr)"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkTermArray(Microsoft.Z3.ArrayExpr)">
            <summary>
            Access the array default value.
            </summary>
            <remarks>
            Produces the default range value, for arrays that can be represented as
            finite maps with a default range value.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkArrayExt(Microsoft.Z3.ArrayExpr,Microsoft.Z3.ArrayExpr)">
            <summary>
            Create Extentionality index. Two arrays are equal if and only if they are equal on the index returned by MkArrayExt.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetSort(Microsoft.Z3.Sort)">
            <summary>
            Create a set type.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkEmptySet(Microsoft.Z3.Sort)">
            <summary>
            Create an empty set.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFullSet(Microsoft.Z3.Sort)">
            <summary>
            Create the full set.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetAdd(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)">
            <summary>
            Add an element to the set.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetDel(Microsoft.Z3.ArrayExpr,Microsoft.Z3.Expr)">
            <summary>
            Remove an element from a set.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetUnion(Microsoft.Z3.ArrayExpr[])">
            <summary>
            Take the union of a list of sets.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetIntersection(Microsoft.Z3.ArrayExpr[])">
            <summary>
            Take the intersection of a list of sets.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetDifference(Microsoft.Z3.ArrayExpr,Microsoft.Z3.ArrayExpr)">
            <summary>
            Take the difference between two sets.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetComplement(Microsoft.Z3.ArrayExpr)">
            <summary>
            Take the complement of a set.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetMembership(Microsoft.Z3.Expr,Microsoft.Z3.ArrayExpr)">
            <summary>
            Check for set membership.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSetSubset(Microsoft.Z3.ArrayExpr,Microsoft.Z3.ArrayExpr)">
            <summary>
            Check for subsetness of sets.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkEmptySeq(Microsoft.Z3.Sort)">
            <summary>
            Create the empty sequence.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUnit(Microsoft.Z3.Expr)">
            <summary>
            Create the singleton sequence.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkString(System.String)">
            <summary>
            Create a string constant.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.IntToString(Microsoft.Z3.Expr)">
            <summary>
            Convert an integer expression to a string.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.UbvToString(Microsoft.Z3.Expr)">
            <summary>
            Convert a bit-vector expression, represented as an unsigned number, to a string.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.SbvToString(Microsoft.Z3.Expr)">
            <summary>
            Convert a bit-vector expression, represented as an signed number, to a string.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.StringToInt(Microsoft.Z3.Expr)">
            <summary>
            Convert an integer expression to a string.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConcat(Microsoft.Z3.SeqExpr[])">
            <summary>
            Concatenate sequences.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkLength(Microsoft.Z3.SeqExpr)">
            <summary>
            Retrieve the length of a given sequence.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPrefixOf(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Check for sequence prefix.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSuffixOf(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Check for sequence suffix.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkContains(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Check for sequence containment of s2 in s1.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkStringLt(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Check if the string s1 is lexicographically strictly less than s2.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkStringLe(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Check if the string s1 is lexicographically less or equal to s2.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAt(Microsoft.Z3.SeqExpr,Microsoft.Z3.Expr)">
            <summary>
            Retrieve sequence of length one at index.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNth(Microsoft.Z3.SeqExpr,Microsoft.Z3.Expr)">
            <summary>
            Retrieve element at index.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkExtract(Microsoft.Z3.SeqExpr,Microsoft.Z3.IntExpr,Microsoft.Z3.IntExpr)">
            <summary>
            Extract subsequence.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIndexOf(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr,Microsoft.Z3.ArithExpr)">
            <summary>
            Extract index of sub-string starting at offset.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReplace(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Replace the first occurrence of src by dst in s.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkToRe(Microsoft.Z3.SeqExpr)">
            <summary>
            Convert a regular expression that accepts sequence s.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInRe(Microsoft.Z3.SeqExpr,Microsoft.Z3.ReExpr)">
            <summary>
            Check for regular expression membership.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkStar(Microsoft.Z3.ReExpr)">
            <summary>
            Take the Kleene star of a regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkLoop(Microsoft.Z3.ReExpr,System.UInt32,System.UInt32)">
            <summary>
            Take the bounded Kleene star of a regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPlus(Microsoft.Z3.ReExpr)">
            <summary>
            Take the Kleene plus of a regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkOption(Microsoft.Z3.ReExpr)">
            <summary>
            Create the optional regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkComplement(Microsoft.Z3.ReExpr)">
            <summary>
            Create the complement regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkConcat(Microsoft.Z3.ReExpr[])">
            <summary>
            Create the concatenation of regular languages.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkUnion(Microsoft.Z3.ReExpr[])">
            <summary>
            Create the union of regular languages.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIntersect(Microsoft.Z3.ReExpr[])">
            <summary>
            Create the intersection of regular languages.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkDiff(Microsoft.Z3.ReExpr,Microsoft.Z3.ReExpr)">
            <summary>
            Create a difference regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkEmptyRe(Microsoft.Z3.Sort)">
            <summary>
            Create the empty regular expression.
            The sort s should be a regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFullRe(Microsoft.Z3.Sort)">
            <summary>
            Create the full regular expression.
            The sort s should be a regular expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkRange(Microsoft.Z3.SeqExpr,Microsoft.Z3.SeqExpr)">
            <summary>
            Create a range expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkCharLe(Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
            Create less than or equal to between two characters.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.CharToInt(Microsoft.Z3.Expr)">
            <summary>
            Create an integer (code point) from character.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.CharToBV(Microsoft.Z3.Expr)">
            <summary>
            Create a bit-vector (code point) from character.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.CharFromBV(Microsoft.Z3.BitVecExpr)">
            <summary>
            Create a character from a bit-vector (code point).
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkIsDigit(Microsoft.Z3.Expr)">
            <summary>
            Create a check if the character is a digit.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAtMost(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr},System.UInt32)">
            <summary>
            Create an at-most-k constraint.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkAtLeast(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr},System.UInt32)">
            <summary>
            Create an at-least-k constraint.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPBLe(System.Int32[],Microsoft.Z3.BoolExpr[],System.Int32)">
            <summary>
            Create a pseudo-Boolean less-or-equal constraint.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPBGe(System.Int32[],Microsoft.Z3.BoolExpr[],System.Int32)">
            <summary>
            Create a pseudo-Boolean greater-or-equal constraint.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkPBEq(System.Int32[],Microsoft.Z3.BoolExpr[],System.Int32)">
            <summary>
            Create a pseudo-Boolean equal constraint.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNumeral(System.String,Microsoft.Z3.Sort)">
            <summary>
            Create a Term of a given sort.
            </summary>
            <param name="v">A string representing the Term value in decimal notation. If the given sort is a real, then the Term can be a rational, that is, a string of the form <c>[num]* / [num]*</c>.</param>
            <param name="ty">The sort of the numeral. In the current implementation, the given sort can be an int, real, or bit-vectors of arbitrary size. </param>
            <returns>A Term with value <paramref name="v"/> and sort <paramref name="ty"/> </returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNumeral(System.Int32,Microsoft.Z3.Sort)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            It is slightly faster than <c>MakeNumeral</c> since it is not necessary to parse a string.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="ty">Sort of the numeral</param>
            <returns>A Term with value <paramref name="v"/> and type <paramref name="ty"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNumeral(System.UInt32,Microsoft.Z3.Sort)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            It is slightly faster than <c>MakeNumeral</c> since it is not necessary to parse a string.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="ty">Sort of the numeral</param>
            <returns>A Term with value <paramref name="v"/> and type <paramref name="ty"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNumeral(System.Int64,Microsoft.Z3.Sort)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            It is slightly faster than <c>MakeNumeral</c> since it is not necessary to parse a string.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="ty">Sort of the numeral</param>
            <returns>A Term with value <paramref name="v"/> and type <paramref name="ty"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkNumeral(System.UInt64,Microsoft.Z3.Sort)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            It is slightly faster than <c>MakeNumeral</c> since it is not necessary to parse a string.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="ty">Sort of the numeral</param>
            <returns>A Term with value <paramref name="v"/> and type <paramref name="ty"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal(System.Int32,System.Int32)">
            <summary>
            Create a real from a fraction.
            </summary>
            <param name="num">numerator of rational.</param>
            <param name="den">denominator of rational.</param>
            <returns>A Term with value <paramref name="num"/>/<paramref name="den"/> and sort Real</returns>
            <seealso cref="M:Microsoft.Z3.Context.MkNumeral(System.String,Microsoft.Z3.Sort)"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal(System.String)">
            <summary>
            Create a real numeral.
            </summary>
            <param name="v">A string representing the Term value in decimal notation.</param>
            <returns>A Term with value <paramref name="v"/> and sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal(System.Int32)">
            <summary>
            Create a real numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal(System.UInt32)">
            <summary>
            Create a real numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal(System.Int64)">
            <summary>
            Create a real numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkReal(System.UInt64)">
            <summary>
            Create a real numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt(System.String)">
            <summary>
            Create an integer numeral.
            </summary>
            <param name="v">A string representing the Term value in decimal notation.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt(System.Int32)">
            <summary>
            Create an integer numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Integer</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt(System.UInt32)">
            <summary>
            Create an integer numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Integer</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt(System.Int64)">
            <summary>
            Create an integer numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Integer</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkInt(System.UInt64)">
            <summary>
            Create an integer numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <returns>A Term with value <paramref name="v"/> and sort Integer</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV(System.String,System.UInt32)">
            <summary>
            Create a bit-vector numeral.
            </summary>
            <param name="v">A string representing the value in decimal notation.</param>
            <param name="size">the size of the bit-vector</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV(System.Int32,System.UInt32)">
            <summary>
            Create a bit-vector numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <param name="size">the size of the bit-vector</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV(System.UInt32,System.UInt32)">
            <summary>
            Create a bit-vector numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <param name="size">the size of the bit-vector</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV(System.Int64,System.UInt32)">
            <summary>
            Create a bit-vector numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <param name="size">the size of the bit-vector</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV(System.UInt64,System.UInt32)">
            <summary>
            Create a bit-vector numeral.
            </summary>
            <param name="v">value of the numeral.</param>
            <param name="size">the size of the bit-vector</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkBV(System.Boolean[])">
            <summary>
            Create a bit-vector numeral.
            </summary>
            <param name="bits">An array of bits representing the bit-vector. Least significant bit is at position 0.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create a universal Quantifier.
            </summary>
            <remarks>
            Creates a forall formula, where <paramref name="weight"/> is the weight,
            <paramref name="patterns"/> is an array of patterns, <paramref name="sorts"/> is an array
            with the sorts of the bound variables, <paramref name="names"/> is an array with the
            'names' of the bound variables, and <paramref name="body"/> is the body of the
            quantifier. Quantifiers are associated with weights indicating the importance of
            using the quantifier during instantiation.
            Note that the bound variables are de-Bruijn indices created using <see cref="M:Microsoft.Z3.Context.MkBound(System.UInt32,Microsoft.Z3.Sort)"/>.
            Z3 applies the convention that the last element in <paramref name="names"/> and
            <paramref name="sorts"/> refers to the variable with index 0, the second to last element
            of <paramref name="names"/> and <paramref name="sorts"/> refers to the variable
            with index 1, etc.
            </remarks>
            <param name="sorts">the sorts of the bound variables.</param>
            <param name="names">names of the bound variables</param>
            <param name="body">the body of the quantifier.</param>
            <param name="weight">quantifiers are associated with weights indicating the importance of using the quantifier during instantiation. By default, pass the weight 0.</param>
            <param name="patterns">array containing the patterns created using <c>MkPattern</c>.</param>
            <param name="noPatterns">array containing the anti-patterns created using <c>MkPattern</c>.</param>
            <param name="quantifierID">optional symbol to track quantifier.</param>
            <param name="skolemID">optional symbol to track skolem constants.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Expr[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create a universal Quantifier.
            </summary>
            <remarks>
            Creates a universal quantifier using a list of constants that will
            form the set of bound variables.
            <seealso cref="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkExists(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create an existential Quantifier.
            </summary>
            <remarks>
            Creates an existential quantifier using de-Bruijn indexed variables.
            (<see cref="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)"/>).
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkExists(Microsoft.Z3.Expr[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create an existential Quantifier.
            </summary>
            <remarks>
            Creates an existential quantifier using a list of constants that will
            form the set of bound variables.
            <seealso cref="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkQuantifier(System.Boolean,Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create a Quantifier.
            </summary>
            <see cref="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkQuantifier(System.Boolean,Microsoft.Z3.Expr[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create a Quantifier.
            </summary>
            <see cref="M:Microsoft.Z3.Context.MkForall(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr,System.UInt32,Microsoft.Z3.Pattern[],Microsoft.Z3.Expr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkLambda(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr)">
            <summary>
            Create a lambda expression.
            </summary>
            <remarks>
            Creates a lambda expression.
            <paramref name="sorts"/> is an array
            with the sorts of the bound variables, <paramref name="names"/> is an array with the
            'names' of the bound variables, and <paramref name="body"/> is the body of the
            lambda. 
            Note that the bound variables are de-Bruijn indices created using <see cref="M:Microsoft.Z3.Context.MkBound(System.UInt32,Microsoft.Z3.Sort)"/>.
            Z3 applies the convention that the last element in <paramref name="names"/> and
            <paramref name="sorts"/> refers to the variable with index 0, the second to last element
            of <paramref name="names"/> and <paramref name="sorts"/> refers to the variable
            with index 1, etc.
            </remarks>
            <param name="sorts">the sorts of the bound variables.</param>
            <param name="names">names of the bound variables</param>
            <param name="body">the body of the quantifier.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkLambda(Microsoft.Z3.Expr[],Microsoft.Z3.Expr)">
            <summary>
            Create a lambda expression.
            </summary>
            <remarks>
            Creates a lambda expression using a list of constants that will
            form the set of bound variables.
            <seealso cref="M:Microsoft.Z3.Context.MkLambda(Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.Expr)"/>
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Context.PrintMode">
            <summary>
            Selects the format used for pretty-printing expressions.
            </summary>
            <remarks>
            The default mode for pretty printing expressions is to produce
            SMT-LIB style output where common subexpressions are printed
            at each occurrence. The mode is called Z3_PRINT_SMTLIB_FULL.
            To print shared common subexpressions only once,
            use the Z3_PRINT_LOW_LEVEL mode.
            To print in way that conforms to SMT-LIB standards and uses let
            expressions to share common sub-expressions use Z3_PRINT_SMTLIB_COMPLIANT.
            </remarks>
            <seealso cref="M:Microsoft.Z3.AST.ToString"/>
            <seealso cref="M:Microsoft.Z3.Pattern.ToString"/>
            <seealso cref="M:Microsoft.Z3.FuncDecl.ToString"/>
            <seealso cref="M:Microsoft.Z3.Sort.ToString"/>
        </member>
        <member name="M:Microsoft.Z3.Context.ParseSMTLIB2String(System.String,Microsoft.Z3.Symbol[],Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.FuncDecl[])">
            <summary>
            Parse the given string using the SMT-LIB2 parser.
            </summary>
            <returns>A conjunction of assertions in the scope (up to push/pop) at the end of the string.</returns>
        </member>
        <member name="M:Microsoft.Z3.Context.ParseSMTLIB2File(System.String,Microsoft.Z3.Symbol[],Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.FuncDecl[])">
            <summary>
            Parse the given file using the SMT-LIB2 parser.
            </summary>
            <seealso cref="M:Microsoft.Z3.Context.ParseSMTLIB2String(System.String,Microsoft.Z3.Symbol[],Microsoft.Z3.Sort[],Microsoft.Z3.Symbol[],Microsoft.Z3.FuncDecl[])"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkGoal(System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Goal.
            </summary>
            <remarks>
            Note that the Context must have been created with proof generation support if
            <paramref name="proofs"/> is set to true here.
            </remarks>
            <param name="models">Indicates whether model generation should be enabled.</param>
            <param name="unsatCores">Indicates whether unsat core generation should be enabled.</param>
            <param name="proofs">Indicates whether proof generation should be enabled.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkParams">
            <summary>
            Creates a new ParameterSet.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.NumTactics">
            <summary>
            The number of supported tactics.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.TacticNames">
            <summary>
            The names of all supported tactics.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.TacticDescription(System.String)">
            <summary>
            Returns a string containing a description of the tactic with the given name.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkTactic(System.String)">
            <summary>
            Creates a new Tactic.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.AndThen(Microsoft.Z3.Tactic,Microsoft.Z3.Tactic,Microsoft.Z3.Tactic[])">
            <summary>
            Create a tactic that applies <paramref name="t1"/> to a Goal and
            then <paramref name="t2"/> to every subgoal produced by <paramref name="t1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Then(Microsoft.Z3.Tactic,Microsoft.Z3.Tactic,Microsoft.Z3.Tactic[])">
            <summary>
            Create a tactic that applies <paramref name="t1"/> to a Goal and
            then <paramref name="t2"/> to every subgoal produced by <paramref name="t1"/>.
            </summary>
            <remarks>
            Shorthand for <c>AndThen</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.OrElse(Microsoft.Z3.Tactic,Microsoft.Z3.Tactic)">
            <summary>
            Create a tactic that first applies <paramref name="t1"/> to a Goal and
            if it fails then returns the result of <paramref name="t2"/> applied to the Goal.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.TryFor(Microsoft.Z3.Tactic,System.UInt32)">
            <summary>
            Create a tactic that applies <paramref name="t"/> to a goal for <paramref name="ms"/> milliseconds.
            </summary>
            <remarks>
            If <paramref name="t"/> does not terminate within <paramref name="ms"/> milliseconds, then it fails.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.When(Microsoft.Z3.Probe,Microsoft.Z3.Tactic)">
            <summary>
            Create a tactic that applies <paramref name="t"/> to a given goal if the probe
            <paramref name="p"/> evaluates to true.
            </summary>
            <remarks>
            If <paramref name="p"/> evaluates to false, then the new tactic behaves like the <c>skip</c> tactic.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.Cond(Microsoft.Z3.Probe,Microsoft.Z3.Tactic,Microsoft.Z3.Tactic)">
            <summary>
            Create a tactic that applies <paramref name="t1"/> to a given goal if the probe
            <paramref name="p"/> evaluates to true and <paramref name="t2"/> otherwise.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Repeat(Microsoft.Z3.Tactic,System.UInt32)">
            <summary>
            Create a tactic that keeps applying <paramref name="t"/> until the goal is not
            modified anymore or the maximum number of iterations <paramref name="max"/> is reached.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Skip">
            <summary>
            Create a tactic that just returns the given goal.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Fail">
            <summary>
            Create a tactic always fails.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.FailIf(Microsoft.Z3.Probe)">
            <summary>
            Create a tactic that fails if the probe <paramref name="p"/> evaluates to false.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.FailIfNotDecided">
            <summary>
            Create a tactic that fails if the goal is not trivially satisfiable (i.e., empty)
            or trivially unsatisfiable (i.e., contains `false').
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.UsingParams(Microsoft.Z3.Tactic,Microsoft.Z3.Params)">
            <summary>
            Create a tactic that applies <paramref name="t"/> using the given set of parameters <paramref name="p"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.With(Microsoft.Z3.Tactic,Microsoft.Z3.Params)">
            <summary>
            Create a tactic that applies <paramref name="t"/> using the given set of parameters <paramref name="p"/>.
            </summary>
            <remarks>Alias for <c>UsingParams</c></remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.ParOr(Microsoft.Z3.Tactic[])">
            <summary>
            Create a tactic that applies the given tactics in parallel until one of them succeeds (i.e., the first that doesn't fail).
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.ParAndThen(Microsoft.Z3.Tactic,Microsoft.Z3.Tactic)">
            <summary>
            Create a tactic that applies <paramref name="t1"/> to a given goal and then <paramref name="t2"/>
            to every subgoal produced by <paramref name="t1"/>. The subgoals are processed in parallel.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Interrupt">
            <summary>
            Interrupt the execution of a Z3 procedure.
            </summary>
            <remarks>This procedure can be used to interrupt: solvers, simplifiers and tactics.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Context.NumSimplifiers">
            <summary>
            The number of supported simplifiers.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.SimplifierNames">
            <summary>
            The names of all supported tactics.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.SimplifierDescription(System.String)">
            <summary>
            Returns a string containing a description of the simplifier with the given name.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSimplifier(System.String)">
            <summary>
            Creates a new Tactic.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.AndThen(Microsoft.Z3.Simplifier,Microsoft.Z3.Simplifier,Microsoft.Z3.Simplifier[])">
            <summary>
            Create a simplifier that applies <paramref name="t1"/> and
            then <paramref name="t2"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Then(Microsoft.Z3.Simplifier,Microsoft.Z3.Simplifier,Microsoft.Z3.Simplifier[])">
            <summary>
            Create a simplifier that applies <paramref name="t1"/> and then
            then <paramref name="t2"/>.
            </summary>
            <remarks>
            Shorthand for <c>AndThen</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.UsingParams(Microsoft.Z3.Simplifier,Microsoft.Z3.Params)">
            <summary>
            Create a tactic that applies <paramref name="t"/> using the given set of parameters <paramref name="p"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.NumProbes">
            <summary>
            The number of supported Probes.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.ProbeNames">
            <summary>
            The names of all supported Probes.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.ProbeDescription(System.String)">
            <summary>
            Returns a string containing a description of the probe with the given name.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkProbe(System.String)">
            <summary>
            Creates a new Probe.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.ConstProbe(System.Double)">
            <summary>
            Create a probe that always evaluates to <paramref name="val"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Lt(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value returned by <paramref name="p1"/>
            is less than the value returned by <paramref name="p2"/>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Gt(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value returned by <paramref name="p1"/>
            is greater than the value returned by <paramref name="p2"/>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Le(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value returned by <paramref name="p1"/>
            is less than or equal the value returned by <paramref name="p2"/>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Ge(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value returned by <paramref name="p1"/>
            is greater than or equal the value returned by <paramref name="p2"/>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Eq(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value returned by <paramref name="p1"/>
            is equal to the value returned by <paramref name="p2"/>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.And(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value <paramref name="p1"/>
            and <paramref name="p2"/> evaluate to "true".
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Or(Microsoft.Z3.Probe,Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value <paramref name="p1"/>
            or <paramref name="p2"/> evaluate to "true".
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Not(Microsoft.Z3.Probe)">
            <summary>
            Create a probe that evaluates to "true" when the value <paramref name="p"/>
            does not evaluate to "true".
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSolver(Microsoft.Z3.Symbol)">
            <summary>
            Creates a new (incremental) solver.
            </summary>
            <remarks>
            This solver also uses a set of builtin tactics for handling the first
            check-sat command, and check-sat commands that take more than a given
            number of milliseconds to be solved.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSolver(System.String)">
            <summary>
            Creates a new (incremental) solver.
            </summary>
            <seealso cref="M:Microsoft.Z3.Context.MkSolver(Microsoft.Z3.Symbol)"/>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSimpleSolver">
            <summary>
            Creates a new (incremental) solver.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSolver(Microsoft.Z3.Solver,Microsoft.Z3.Simplifier)">
            <summary>
            Creates a solver that uses an incremental simplifier.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkSolver(Microsoft.Z3.Tactic)">
            <summary>
            Creates a solver that is implemented using the given tactic.
            </summary>
            <remarks>
            The solver supports the commands <c>Push</c> and <c>Pop</c>, but it
            will always solve each check from scratch.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFixedpoint">
            <summary>
            Create a Fixedpoint context.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkOptimize">
            <summary>
            Create an Optimization context.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundingModeSort">
            <summary>
            Create the floating-point RoundingMode sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundNearestTiesToEven">
            <summary>
            Create a numeral of RoundingMode sort which represents the NearestTiesToEven rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRNE">
            <summary>
            Create a numeral of RoundingMode sort which represents the NearestTiesToEven rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundNearestTiesToAway">
            <summary>
            Create a numeral of RoundingMode sort which represents the NearestTiesToAway rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRNA">
            <summary>
            Create a numeral of RoundingMode sort which represents the NearestTiesToAway rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundTowardPositive">
            <summary>
            Create a numeral of RoundingMode sort which represents the RoundTowardPositive rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRTP">
            <summary>
            Create a numeral of RoundingMode sort which represents the RoundTowardPositive rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundTowardNegative">
            <summary>
            Create a numeral of RoundingMode sort which represents the RoundTowardNegative rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRTN">
            <summary>
            Create a numeral of RoundingMode sort which represents the RoundTowardNegative rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundTowardZero">
            <summary>
            Create a numeral of RoundingMode sort which represents the RoundTowardZero rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRTZ">
            <summary>
            Create a numeral of RoundingMode sort which represents the RoundTowardZero rounding mode.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSort(System.UInt32,System.UInt32)">
            <summary>
            Create a FloatingPoint sort.
            </summary>
            <param name="ebits">exponent bits in the FloatingPoint sort.</param>
            <param name="sbits">significand bits in the FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSortHalf">
            <summary>
            Create the half-precision (16-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSort16">
            <summary>
            Create the half-precision (16-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSortSingle">
            <summary>
            Create the single-precision (32-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSort32">
            <summary>
            Create the single-precision (32-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSortDouble">
            <summary>
            Create the double-precision (64-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSort64">
            <summary>
            Create the double-precision (64-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSortQuadruple">
            <summary>
            Create the quadruple-precision (128-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSort128">
            <summary>
            Create the quadruple-precision (128-bit) FloatingPoint sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNaN(Microsoft.Z3.FPSort)">
            <summary>
            Create a NaN of sort s.
            </summary>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPInf(Microsoft.Z3.FPSort,System.Boolean)">
            <summary>
            Create a floating-point infinity of sort s.
            </summary>
            <param name="s">FloatingPoint sort.</param>
            <param name="negative">indicates whether the result should be negative.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPZero(Microsoft.Z3.FPSort,System.Boolean)">
            <summary>
            Create a floating-point zero of sort s.
            </summary>
            <param name="s">FloatingPoint sort.</param>
            <param name="negative">indicates whether the result should be negative.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNumeral(System.Single,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a float.
            </summary>
            <param name="v">numeral value.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNumeral(System.Double,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a float.
            </summary>
            <param name="v">numeral value.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNumeral(System.Int32,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from an int.
            </summary>
            <param name="v">numeral value.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNumeral(System.Boolean,System.UInt32,System.Int32,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a sign bit and two integers.
            </summary>
            <param name="sgn">the sign.</param>
            <param name="sig">the significand.</param>
            <param name="exp">the exponent.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNumeral(System.Boolean,System.Int64,System.UInt64,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a sign bit and two 64-bit integers.
            </summary>
            <param name="sgn">the sign.</param>
            <param name="sig">the significand.</param>
            <param name="exp">the exponent.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFP(System.Single,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a float.
            </summary>
            <param name="v">numeral value.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFP(System.Double,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a float.
            </summary>
            <param name="v">numeral value.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFP(System.Int32,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from an int.
            </summary>
            <param name="v">numeral value.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFP(System.Boolean,System.Int32,System.UInt32,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a sign bit and two integers.
            </summary>
            <param name="sgn">the sign.</param>
            <param name="exp">the exponent.</param>
            <param name="sig">the significand.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFP(System.Boolean,System.Int64,System.UInt64,Microsoft.Z3.FPSort)">
            <summary>
            Create a numeral of FloatingPoint sort from a sign bit and two 64-bit integers.
            </summary>
            <param name="sgn">the sign.</param>
            <param name="exp">the exponent.</param>
            <param name="sig">the significand.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPAbs(Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point absolute value
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPNeg(Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point negation
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPAdd(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point addition
            </summary>
            <param name="rm">rounding mode term</param>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSub(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point subtraction
            </summary>
            <param name="rm">rounding mode term</param>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPMul(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point multiplication
            </summary>
            <param name="rm">rounding mode term</param>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPDiv(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point division
            </summary>
            <param name="rm">rounding mode term</param>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPFMA(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point fused multiply-add
            </summary>
            <remarks>
            The result is round((t1 * t2) + t3)
            </remarks>
            <param name="rm">rounding mode term</param>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
            <param name="t3">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPSqrt(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point square root
            </summary>
            <param name="rm">rounding mode term</param>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRem(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point remainder
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPRoundToIntegral(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point roundToIntegral. Rounds a floating-point number to
            the closest integer, again represented as a floating-point number.
            </summary>
            <param name="rm">term of RoundingMode sort</param>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPMin(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Minimum of floating-point numbers.
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPMax(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Maximum of floating-point numbers.
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPLEq(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point less than or equal.
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPLt(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point less than.
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPGEq(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point greater than or equal.
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPGt(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point greater than.
            </summary>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPEq(Microsoft.Z3.FPExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Floating-point equality.
            </summary>
            <remarks>
            Note that this is IEEE 754 equality (as opposed to standard =).
            </remarks>
            <param name="t1">floating-point term</param>
            <param name="t2">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsNormal(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a normal floating-point number.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsSubnormal(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a subnormal floating-point number.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsZero(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a floating-point number with zero value, i.e., +0 or -0.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsInfinite(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a floating-point number representing +oo or -oo.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsNaN(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a NaN.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsNegative(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a negative floating-point number.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPIsPositive(Microsoft.Z3.FPExpr)">
            <summary>
            Predicate indicating whether t is a positive floating-point number.
            </summary>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFP(Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr,Microsoft.Z3.BitVecExpr)">
            <summary>
            Create an expression of FloatingPoint sort from three bit-vector expressions.
            </summary>
            <remarks>
            This is the operator named `fp' in the SMT FP theory definition.
            Note that sgn is required to be a bit-vector of size 1. Significand and exponent
            are required to be greater than 1 and 2 respectively. The FloatingPoint sort
            of the resulting expression is automatically determined from the bit-vector sizes
            of the arguments.
            </remarks>
            <param name="sgn">bit-vector term (of size 1) representing the sign.</param>
            <param name="sig">bit-vector term representing the significand.</param>
            <param name="exp">bit-vector term representing the exponent.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToFP(Microsoft.Z3.BitVecExpr,Microsoft.Z3.FPSort)">
            <summary>
            Conversion of a single IEEE 754-2008 bit-vector into a floating-point number.
            </summary>
            <remarks>
            Produces a term that represents the conversion of a bit-vector term bv to a
            floating-point term of sort s. The bit-vector size of bv (m) must be equal
            to ebits+sbits of s. The format of the bit-vector is as defined by the
            IEEE 754-2008 interchange format.
            </remarks>
            <param name="bv">bit-vector value (of size m).</param>
            <param name="s">FloatingPoint sort (ebits+sbits == m)</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToFP(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,Microsoft.Z3.FPSort)">
            <summary>
            Conversion of a FloatingPoint term into another term of different FloatingPoint sort.
            </summary>
            <remarks>
            Produces a term that represents the conversion of a floating-point term t to a
            floating-point term of sort s. If necessary, the result will be rounded according
            to rounding mode rm.
            </remarks>
            <param name="rm">RoundingMode term.</param>
            <param name="t">FloatingPoint term.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToFP(Microsoft.Z3.FPRMExpr,Microsoft.Z3.RealExpr,Microsoft.Z3.FPSort)">
            <summary>
            Conversion of a term of real sort into a term of FloatingPoint sort.
            </summary>
            <remarks>
            Produces a term that represents the conversion of term t of real sort into a
            floating-point term of sort s. If necessary, the result will be rounded according
            to rounding mode rm.
            </remarks>
            <param name="rm">RoundingMode term.</param>
            <param name="t">term of Real sort.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToFP(Microsoft.Z3.FPRMExpr,Microsoft.Z3.BitVecExpr,Microsoft.Z3.FPSort,System.Boolean)">
            <summary>
            Conversion of a 2's complement signed bit-vector term into a term of FloatingPoint sort.
            </summary>
            <remarks>
            Produces a term that represents the conversion of the bit-vector term t into a
            floating-point term of sort s. The bit-vector t is taken to be in signed
            2's complement format (when signed==true, otherwise unsigned). If necessary, the
            result will be rounded according to rounding mode rm.
            </remarks>
            <param name="rm">RoundingMode term.</param>
            <param name="t">term of bit-vector sort.</param>
            <param name="s">FloatingPoint sort.</param>
            <param name="signed">flag indicating whether t is interpreted as signed or unsigned bit-vector.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToFP(Microsoft.Z3.FPSort,Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr)">
            <summary>
            Conversion of a floating-point number to another FloatingPoint sort s.
            </summary>
            <remarks>
            Produces a term that represents the conversion of a floating-point term t to a different
            FloatingPoint sort s. If necessary, rounding according to rm is applied.
            </remarks>
            <param name="s">FloatingPoint sort</param>
            <param name="rm">floating-point rounding mode term</param>
            <param name="t">floating-point term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToBV(Microsoft.Z3.FPRMExpr,Microsoft.Z3.FPExpr,System.UInt32,System.Boolean)">
            <summary>
            Conversion of a floating-point term into a bit-vector.
            </summary>
            <remarks>
            Produces a term that represents the conversion of the floating-point term t into a
            bit-vector term of size sz in 2's complement format (signed when sign==true). If necessary,
            the result will be rounded according to rounding mode rm.
            </remarks>
            <param name="rm">RoundingMode term.</param>
            <param name="t">FloatingPoint term</param>
            <param name="sz">Size of the resulting bit-vector.</param>
            <param name="sign">Indicates whether the result is a signed or unsigned bit-vector.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToReal(Microsoft.Z3.FPExpr)">
            <summary>
            Conversion of a floating-point term into a real-numbered term.
            </summary>
            <remarks>
            Produces a term that represents the conversion of the floating-point term t into a
            real number. Note that this type of conversion will often result in non-linear
            constraints over real terms.
            </remarks>
            <param name="t">FloatingPoint term</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToIEEEBV(Microsoft.Z3.FPExpr)">
            <summary>
            Conversion of a floating-point term into a bit-vector term in IEEE 754-2008 format.
            </summary>
            <remarks>
            The size of the resulting bit-vector is automatically determined. Note that
            IEEE 754-2008 allows multiple different representations of NaN. This conversion
            knows only one NaN and it will always produce the same bit-vector representation of
            that NaN.
            </remarks>
            <param name="t">FloatingPoint term.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.MkFPToFP(Microsoft.Z3.FPRMExpr,Microsoft.Z3.IntExpr,Microsoft.Z3.RealExpr,Microsoft.Z3.FPSort)">
            <summary>
            Conversion of a real-sorted significand and an integer-sorted exponent into a term of FloatingPoint sort.
            </summary>
            <remarks>
            Produces a term that represents the conversion of sig * 2^exp into a
            floating-point term of sort s. If necessary, the result will be rounded
            according to rounding mode rm.
            </remarks>
            <param name="rm">RoundingMode term.</param>
            <param name="exp">Exponent term of Int sort.</param>
            <param name="sig">Significand term of Real sort.</param>
            <param name="s">FloatingPoint sort.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.WrapAST(System.IntPtr)">
            <summary>
            Wraps an AST.
            </summary>
            <remarks>This function is used for transitions between native and
            managed objects. Note that <paramref name="nativeObject"/> must be a
            native object obtained from Z3 (e.g., through <seealso cref="M:Microsoft.Z3.Context.UnwrapAST(Microsoft.Z3.AST)"/>)
            and that it must have a correct reference count (see e.g.,
            <seealso cref="M:Microsoft.Z3.Native.Z3_inc_ref(System.IntPtr,System.IntPtr)"/>.</remarks>
            <seealso cref="M:Microsoft.Z3.Context.UnwrapAST(Microsoft.Z3.AST)"/>
            <param name="nativeObject">The native pointer to wrap.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.UnwrapAST(Microsoft.Z3.AST)">
            <summary>
            Unwraps an AST.
            </summary>
            <remarks>This function is used for transitions between native and
            managed objects. It returns the native pointer to the AST. Note that
            AST objects are reference counted and unwrapping an AST disables automatic
            reference counting, i.e., all references to the IntPtr that is returned
            must be handled externally and through native calls (see e.g.,
            <seealso cref="M:Microsoft.Z3.Native.Z3_inc_ref(System.IntPtr,System.IntPtr)"/>).</remarks>
            <seealso cref="M:Microsoft.Z3.Context.WrapAST(System.IntPtr)"/>
            <param name="a">The AST to unwrap.</param>
        </member>
        <member name="M:Microsoft.Z3.Context.SimplifyHelp">
            <summary>
            Return a string describing all available parameters to <c>Expr.Simplify</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Context.SimplifyParameterDescriptions">
            <summary>
            Retrieves parameter descriptions for simplifier.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.UpdateParamValue(System.String,System.String)">
            <summary>
            Update a mutable configuration parameter.
            </summary>
            <remarks>
            The list of all configuration parameters can be obtained using the Z3 executable:
            <c>z3.exe -p</c>
            Only a few configuration parameters are mutable once the context is created.
            An exception is thrown when trying to modify an immutable parameter.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Context.Finalize">
            <summary>
            Finalizer.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Context.Dispose">
            <summary>
            Disposes of the context.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.DatatypeExpr">
            <summary>
            Datatype expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.DatatypeExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for DatatypeExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.DatatypeSort">
            <summary>
            Datatype sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.DatatypeSort.NumConstructors">
            <summary>
            The number of constructors of the datatype sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.DatatypeSort.Constructors">
            <summary>
            The constructors.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.DatatypeSort.Recognizers">
            <summary>
            The recognizers.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.DatatypeSort.Accessors">
            <summary>
            The constructor accessors.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.EnumSort">
            <summary>
            Enumeration sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.EnumSort.ConstDecls">
            <summary>
            The function declarations of the constants in the enumeration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.EnumSort.ConstDecl(System.UInt32)">
            <summary>
            Retrieves the inx'th constant declaration in the enumeration.
            </summary>
            <param name="inx"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Z3.EnumSort.Consts">
            <summary>
            The constants in the enumeration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.EnumSort.Const(System.UInt32)">
            <summary>
            Retrieves the inx'th constant in the enumeration.
            </summary>
            <param name="inx"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Z3.EnumSort.TesterDecls">
            <summary>
            The test predicates (recognizers) for the constants in the enumeration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.EnumSort.TesterDecl(System.UInt32)">
            <summary>
            Retrieves the inx'th tester/recognizer declaration in the enumeration.
            </summary>
            <param name="inx"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Z3.Expr">
            <summary>
            Expressions are terms.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Expr.Simplify(Microsoft.Z3.Params)">
            <summary>
            Returns a simplified version of the expression.
            </summary>
            <param name="p">A set of parameters to configure the simplifier</param>
            <seealso cref="M:Microsoft.Z3.Context.SimplifyHelp"/>
        </member>
        <member name="P:Microsoft.Z3.Expr.FuncDecl">
            <summary>
            The function declaration of the function that is applied in this expression.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.BoolValue">
            <summary>
            Indicates whether the expression is the true or false expression
            or something else (Z3_L_UNDEF).
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.NumArgs">
            <summary>
            The number of arguments of the expression.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.Args">
            <summary>
            The arguments of the expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Expr.Arg(System.UInt32)">
            <summary>
            The i'th argument of the expression.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Expr.Update(Microsoft.Z3.Expr[])">
            <summary>
            Update the arguments of the expression using the arguments <paramref name="args"/>
            The number of new arguments should coincide with the current number of arguments.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Expr.Substitute(Microsoft.Z3.Expr[],Microsoft.Z3.Expr[])">
            <summary>
            Substitute every occurrence of <c>from[i]</c> in the expression with <c>to[i]</c>, for <c>i</c> smaller than <c>num_exprs</c>.
            </summary>
            <remarks>
            The result is the new expression. The arrays <c>from</c> and <c>to</c> must have size <c>num_exprs</c>.
            For every <c>i</c> smaller than <c>num_exprs</c>, we must have that
            sort of <c>from[i]</c> must be equal to sort of <c>to[i]</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Expr.Substitute(Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
            Substitute every occurrence of <c>from</c> in the expression with <c>to</c>.
            </summary>
            <seealso cref="M:Microsoft.Z3.Expr.Substitute(Microsoft.Z3.Expr[],Microsoft.Z3.Expr[])"/>
        </member>
        <member name="M:Microsoft.Z3.Expr.SubstituteVars(Microsoft.Z3.Expr[])">
            <summary>
            Substitute the free variables in the expression with the expressions in <paramref name="to"/>
            </summary>
            <remarks>
            For every <c>i</c> smaller than <c>num_exprs</c>, the variable with de-Bruijn index <c>i</c> is replaced with term <c>to[i]</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Expr.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the term to the Context <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A copy of the term which is associated with <paramref name="ctx"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Expr.Dup">
            <summary>
            Create a duplicate of expression. 
            This feature is to allow extending the life-time of expressions that were passed down as arguments
            by the user propagator callbacks. By default the life-time of arguments to callbacks is within the
            callback only.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Expr.ToString">
            <summary>
            Returns a string representation of the expression.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsNumeral">
            <summary>
            Indicates whether the term is a numeral
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsWellSorted">
            <summary>
            Indicates whether the term is well-sorted.
            </summary>
            <returns>True if the term is well-sorted, false otherwise.</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.Sort">
            <summary>
            The Sort of the term.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsConst">
            <summary>
            Indicates whether the term represents a constant.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIntNum">
            <summary>
            Indicates whether the term is an integer numeral.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRatNum">
            <summary>
            Indicates whether the term is a real numeral.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAlgebraicNumber">
            <summary>
            Indicates whether the term is an algebraic number
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBool">
            <summary>
            Indicates whether the term has Boolean sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsTrue">
            <summary>
            Indicates whether the term is the constant true.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFalse">
            <summary>
            Indicates whether the term is the constant false.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsEq">
            <summary>
            Indicates whether the term is an equality predicate.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsDistinct">
            <summary>
            Indicates whether the term is an n-ary distinct predicate (every argument is mutually distinct).
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsITE">
            <summary>
            Indicates whether the term is a ternary if-then-else term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAnd">
            <summary>
            Indicates whether the term is an n-ary conjunction
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsOr">
            <summary>
            Indicates whether the term is an n-ary disjunction
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIff">
            <summary>
            Indicates whether the term is an if-and-only-if (Boolean equivalence, binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsXor">
            <summary>
            Indicates whether the term is an exclusive or
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsNot">
            <summary>
            Indicates whether the term is a negation
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsImplies">
            <summary>
            Indicates whether the term is an implication
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAtMost">
            <summary>
            Indicates whether the term is at-most
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.AtMostBound">
            <summary>
            Retrieve bound of at-most
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAtLeast">
            <summary>
            Indicates whether the term is at-least
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.AtLeastBound">
            <summary>
            Retrieve bound of at-least
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsPbEq">
            <summary>
            Indicates whether the term is pbeq
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsPbLe">
            <summary>
            Indicates whether the term is pble
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsPbGe">
            <summary>
            Indicates whether the term is pbge
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsInt">
            <summary>
            Indicates whether the term is of integer sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsReal">
            <summary>
            Indicates whether the term is of sort real.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsArithmeticNumeral">
            <summary>
            Indicates whether the term is an arithmetic numeral.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsLE">
            <summary>
            Indicates whether the term is a less-than-or-equal
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsGE">
            <summary>
            Indicates whether the term is a greater-than-or-equal
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsLT">
            <summary>
            Indicates whether the term is a less-than
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsGT">
            <summary>
            Indicates whether the term is a greater-than
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAdd">
            <summary>
            Indicates whether the term is addition (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSub">
            <summary>
            Indicates whether the term is subtraction (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsUMinus">
            <summary>
            Indicates whether the term is a unary minus
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsMul">
            <summary>
            Indicates whether the term is multiplication (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsDiv">
            <summary>
            Indicates whether the term is division (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIDiv">
            <summary>
            Indicates whether the term is integer division (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRemainder">
            <summary>
            Indicates whether the term is remainder (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsModulus">
            <summary>
            Indicates whether the term is modulus (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIntToReal">
            <summary>
            Indicates whether the term is a coercion of integer to real (unary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRealToInt">
            <summary>
            Indicates whether the term is a coercion of real to integer (unary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRealIsInt">
            <summary>
            Indicates whether the term is a check that tests whether a real is integral (unary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsArray">
            <summary>
            Indicates whether the term is of an array sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsStore">
            <summary>
            Indicates whether the term is an array store.
            </summary>
            <remarks>It satisfies select(store(a,i,v),j) = if i = j then v else select(a,j).
            Array store takes at least 3 arguments. </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSelect">
            <summary>
            Indicates whether the term is an array select.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsConstantArray">
            <summary>
            Indicates whether the term is a constant array.
            </summary>
            <remarks>For example, select(const(v),i) = v holds for every v and i. The function is unary.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsDefaultArray">
            <summary>
            Indicates whether the term is a default array.
            </summary>
            <remarks>For example default(const(v)) = v. The function is unary.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsArrayMap">
            <summary>
            Indicates whether the term is an array map.
            </summary>
            <remarks>It satisfies map[f](a1,..,a_n)[i] = f(a1[i],...,a_n[i]) for every i.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAsArray">
            <summary>
            Indicates whether the term is an as-array term.
            </summary>
            <remarks>An as-array term is n array value that behaves as the function graph of the
            function passed as parameter.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSetUnion">
            <summary>
            Indicates whether the term is set union
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSetIntersect">
            <summary>
            Indicates whether the term is set intersection
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSetDifference">
            <summary>
            Indicates whether the term is set difference
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSetComplement">
            <summary>
            Indicates whether the term is set complement
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSetSubset">
            <summary>
            Indicates whether the term is set subset
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBV">
            <summary>
             Indicates whether the terms is of bit-vector sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVNumeral">
            <summary>
            Indicates whether the term is a bit-vector numeral
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVBitOne">
            <summary>
            Indicates whether the term is a one-bit bit-vector with value one
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVBitZero">
            <summary>
            Indicates whether the term is a one-bit bit-vector with value zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVUMinus">
            <summary>
            Indicates whether the term is a bit-vector unary minus
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVAdd">
            <summary>
            Indicates whether the term is a bit-vector addition (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSub">
            <summary>
            Indicates whether the term is a bit-vector subtraction (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVMul">
            <summary>
            Indicates whether the term is a bit-vector multiplication (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSDiv">
            <summary>
            Indicates whether the term is a bit-vector signed division (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVUDiv">
            <summary>
            Indicates whether the term is a bit-vector unsigned division (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSRem">
            <summary>
            Indicates whether the term is a bit-vector signed remainder (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVURem">
            <summary>
            Indicates whether the term is a bit-vector unsigned remainder (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSMod">
            <summary>
            Indicates whether the term is a bit-vector signed modulus
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSDiv0">
            <summary>
            Indicates whether the term is a bit-vector signed division by zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVUDiv0">
            <summary>
            Indicates whether the term is a bit-vector unsigned division by zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSRem0">
            <summary>
            Indicates whether the term is a bit-vector signed remainder by zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVURem0">
            <summary>
            Indicates whether the term is a bit-vector unsigned remainder by zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSMod0">
            <summary>
            Indicates whether the term is a bit-vector signed modulus by zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVULE">
            <summary>
            Indicates whether the term is an unsigned bit-vector less-than-or-equal
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSLE">
            <summary>
            Indicates whether the term is a signed bit-vector less-than-or-equal
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVUGE">
            <summary>
            Indicates whether the term is an unsigned bit-vector greater-than-or-equal
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSGE">
            <summary>
            Indicates whether the term is a signed bit-vector greater-than-or-equal
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVULT">
            <summary>
            Indicates whether the term is an unsigned bit-vector less-than
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSLT">
            <summary>
            Indicates whether the term is a signed bit-vector less-than
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVUGT">
            <summary>
            Indicates whether the term is an unsigned bit-vector greater-than
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSGT">
            <summary>
            Indicates whether the term is a signed bit-vector greater-than
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVAND">
            <summary>
            Indicates whether the term is a bit-wise AND
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVOR">
            <summary>
            Indicates whether the term is a bit-wise OR
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVNOT">
            <summary>
            Indicates whether the term is a bit-wise NOT
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVXOR">
            <summary>
            Indicates whether the term is a bit-wise XOR
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVNAND">
            <summary>
            Indicates whether the term is a bit-wise NAND
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVNOR">
            <summary>
            Indicates whether the term is a bit-wise NOR
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVXNOR">
            <summary>
            Indicates whether the term is a bit-wise XNOR
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVConcat">
            <summary>
            Indicates whether the term is a bit-vector concatenation (binary)
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVSignExtension">
            <summary>
            Indicates whether the term is a bit-vector sign extension
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVZeroExtension">
            <summary>
            Indicates whether the term is a bit-vector zero extension
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVExtract">
            <summary>
            Indicates whether the term is a bit-vector extraction
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVRepeat">
            <summary>
            Indicates whether the term is a bit-vector repetition
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVReduceOR">
            <summary>
            Indicates whether the term is a bit-vector reduce OR
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVReduceAND">
            <summary>
            Indicates whether the term is a bit-vector reduce AND
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVComp">
            <summary>
            Indicates whether the term is a bit-vector comparison
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVShiftLeft">
            <summary>
            Indicates whether the term is a bit-vector shift left
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVShiftRightLogical">
            <summary>
            Indicates whether the term is a bit-vector logical shift right
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVShiftRightArithmetic">
            <summary>
            Indicates whether the term is a bit-vector arithmetic shift left
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVRotateLeft">
            <summary>
            Indicates whether the term is a bit-vector rotate left
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVRotateRight">
            <summary>
            Indicates whether the term is a bit-vector rotate right
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVRotateLeftExtended">
            <summary>
            Indicates whether the term is a bit-vector rotate left (extended)
            </summary>
            <remarks>Similar to Z3_OP_ROTATE_LEFT, but it is a binary operator instead of a parametric one.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVRotateRightExtended">
            <summary>
            Indicates whether the term is a bit-vector rotate right (extended)
            </summary>
            <remarks>Similar to Z3_OP_ROTATE_RIGHT, but it is a binary operator instead of a parametric one.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIntToBV">
            <summary>
            Indicates whether the term is a coercion from integer to bit-vector
            </summary>
            <remarks>This function is not supported by the decision procedures. Only the most
            rudimentary simplification rules are applied to this function.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVToInt">
            <summary>
            Indicates whether the term is a coercion from bit-vector to integer
            </summary>
            <remarks>This function is not supported by the decision procedures. Only the most
            rudimentary simplification rules are applied to this function.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVCarry">
            <summary>
            Indicates whether the term is a bit-vector carry
            </summary>
            <remarks>Compute the carry bit in a full-adder.  The meaning is given by the
            equivalence (carry l1 l2 l3) &lt;=&gt; (or (and l1 l2) (and l1 l3) (and l2 l3)))</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsBVXOR3">
            <summary>
            Indicates whether the term is a bit-vector ternary XOR
            </summary>
            <remarks>The meaning is given by the equivalence (xor3 l1 l2 l3) &lt;=&gt; (xor (xor l1 l2) l3)</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsLabel">
            <summary>
            Indicates whether the term is a label (used by the Boogie Verification condition generator).
            </summary>
            <remarks>The label has two parameters, a string and a Boolean polarity. It takes one argument, a formula.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsLabelLit">
            <summary>
            Indicates whether the term is a label literal (used by the Boogie Verification condition generator).
            </summary>
            <remarks>A label literal has a set of string parameters. It takes no arguments.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsString">
            <summary>
            Check whether expression is a string constant.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.String">
            <summary>
            Retrieve string corresponding to string constant.
            </summary>
            <remarks>the expression should be a string constant, (IsString should be true).</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsConcat">
            <summary>
            Check whether expression is a concatenation.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsPrefix">
            <summary>
            Check whether expression is a prefix.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsSuffix">
            <summary>
            Check whether expression is a suffix.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsContains">
            <summary>
            Check whether expression is a contains.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsExtract">
            <summary>
            Check whether expression is an extract.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsReplace">
            <summary>
            Check whether expression is a replace.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsAt">
            <summary>
            Check whether expression is an at.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsLength">
            <summary>
            Check whether expression is a sequence length.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIndex">
            <summary>
            Check whether expression is a sequence index.
            </summary>
            <returns>a Boolean</returns>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsOEQ">
            <summary>
            Indicates whether the term is a binary equivalence modulo namings.
            </summary>
            <remarks>This binary predicate is used in proof terms.
            It captures equisatisfiability and equivalence modulo renamings.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofTrue">
            <summary>
            Indicates whether the term is a Proof for the expression 'true'.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofAsserted">
            <summary>
            Indicates whether the term is a proof for a fact asserted by the user.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofGoal">
            <summary>
            Indicates whether the term is a proof for a fact (tagged as goal) asserted by the user.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofModusPonens">
            <summary>
            Indicates whether the term is proof via modus ponens
            </summary>
            <remarks>
            Given a proof for p and a proof for (implies p q), produces a proof for q.
            T1: p
            T2: (implies p q)
            [mp T1 T2]: q
            The second antecedents may also be a proof for (iff p q).</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofReflexivity">
            <summary>
            Indicates whether the term is a proof for (R t t), where R is a reflexive relation.
            </summary>
            <remarks>This proof object has no antecedents.
            The only reflexive relations that are used are
            equivalence modulo namings, equality and equivalence.
            That is, R is either '~', '=' or 'iff'.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofSymmetry">
            <summary>
            Indicates whether the term is proof by symmetricity of a relation
            </summary>
            <remarks>
            Given an symmetric relation R and a proof for (R t s), produces a proof for (R s t).
            T1: (R t s)
            [symmetry T1]: (R s t)
            T1 is the antecedent of this proof object.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofTransitivity">
            <summary>
            Indicates whether the term is a proof by transitivity of a relation
            </summary>
            <remarks>
            Given a transitive relation R, and proofs for (R t s) and (R s u), produces a proof
            for (R t u).
            T1: (R t s)
            T2: (R s u)
            [trans T1 T2]: (R t u)
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofTransitivityStar">
             <summary>
             Indicates whether the term is a proof by condensed transitivity of a relation
             </summary>
             <remarks>
             Condensed transitivity proof. 
             It combines several symmetry and transitivity proofs.
             Example:
             T1: (R a b)
             T2: (R c b)
             T3: (R c d)
             [trans* T1 T2 T3]: (R a d)
             R must be a symmetric and transitive relation.
            
             Assuming that this proof object is a proof for (R s t), then
             a proof checker must check if it is possible to prove (R s t)
             using the antecedents, symmetry and transitivity.  That is,
             if there is a path from s to t, if we view every
             antecedent (R a b) as an edge between a and b.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofMonotonicity">
            <summary>
            Indicates whether the term is a monotonicity proof object.
            </summary>
            <remarks>
            T1: (R t_1 s_1)
            ...
            Tn: (R t_n s_n)
            [monotonicity T1 ... Tn]: (R (f t_1 ... t_n) (f s_1 ... s_n))
            Remark: if t_i == s_i, then the antecedent Ti is suppressed.
            That is, reflexivity proofs are suppressed to save space.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofQuantIntro">
            <summary>
            Indicates whether the term is a quant-intro proof
            </summary>
            <remarks>
            Given a proof for (~ p q), produces a proof for (~ (forall (x) p) (forall (x) q)).
            T1: (~ p q)
            [quant-intro T1]: (~ (forall (x) p) (forall (x) q))
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofDistributivity">
             <summary>
             Indicates whether the term is a distributivity proof object.
             </summary>
             <remarks>
             Given that f (= or) distributes over g (= and), produces a proof for
             (= (f a (g c d))
             (g (f a c) (f a d)))
             If f and g are associative, this proof also justifies the following equality:
             (= (f (g a b) (g c d))
             (g (f a c) (f a d) (f b c) (f b d)))
             where each f and g can have arbitrary number of arguments.
            
             This proof object has no antecedents.
             Remark. This rule is used by the CNF conversion pass and
             instantiated by f = or, and g = and.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofAndElimination">
            <summary>
            Indicates whether the term is a proof by elimination of AND
            </summary>
            <remarks>
            Given a proof for (and l_1 ... l_n), produces a proof for l_i
            T1: (and l_1 ... l_n)
            [and-elim T1]: l_i
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofOrElimination">
            <summary>
            Indicates whether the term is a proof by elimination of not-or
            </summary>
            <remarks>
            Given a proof for (not (or l_1 ... l_n)), produces a proof for (not l_i).
            T1: (not (or l_1 ... l_n))
            [not-or-elim T1]: (not l_i)
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofRewrite">
             <summary>
             Indicates whether the term is a proof by rewriting
             </summary>
             <remarks>
             A proof for a local rewriting step (= t s).
             The head function symbol of t is interpreted.
            
             This proof object has no antecedents.
             The conclusion of a rewrite rule is either an equality (= t s),
             an equivalence (iff t s), or equi-satisfiability (~ t s).
             Remark: if f is bool, then = is iff.
            
             Examples:
             (= (+ x 0) x)
             (= (+ x 1 2) (+ 3 x))
             (iff (or x false) x)
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofRewriteStar">
            <summary>
            Indicates whether the term is a proof by rewriting
            </summary>
            <remarks>
            A proof for rewriting an expression t into an expression s.
            This proof object can have n antecedents.
            The antecedents are proofs for equalities used as substitution rules.
            The object is used in a few cases:
            - When applying contextual simplification (CONTEXT_SIMPLIFIER=true)
            - When converting bit-vectors to Booleans (BIT2BOOL=true)
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofPullQuant">
            <summary>
            Indicates whether the term is a proof for pulling quantifiers out.
            </summary>
            <remarks>
            A proof for (iff (f (forall (x) q(x)) r) (forall (x) (f (q x) r))). This proof object has no antecedents.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofPushQuant">
            <summary>
            Indicates whether the term is a proof for pushing quantifiers in.
            </summary>
            <remarks>
            A proof for:
            (iff (forall (x_1 ... x_m) (and p_1[x_1 ... x_m] ... p_n[x_1 ... x_m]))
                    (and (forall (x_1 ... x_m) p_1[x_1 ... x_m])
                     ...
                 (forall (x_1 ... x_m) p_n[x_1 ... x_m])))
             This proof object has no antecedents
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofElimUnusedVars">
             <summary>
             Indicates whether the term is a proof for elimination of unused variables.
             </summary>
             <remarks>
             A proof for (iff (forall (x_1 ... x_n y_1 ... y_m) p[x_1 ... x_n])
                              (forall (x_1 ... x_n) p[x_1 ... x_n]))
            
             It is used to justify the elimination of unused variables.
             This proof object has no antecedents.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofDER">
             <summary>
             Indicates whether the term is a proof for destructive equality resolution
             </summary>
             <remarks>
             A proof for destructive equality resolution:
             (iff (forall (x) (or (not (= x t)) P[x])) P[t])
             if x does not occur in t.
            
             This proof object has no antecedents.
            
             Several variables can be eliminated simultaneously.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofQuantInst">
            <summary>
            Indicates whether the term is a proof for quantifier instantiation
            </summary>
            <remarks>
            A proof of (or (not (forall (x) (P x))) (P a))
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofHypothesis">
            <summary>
            Indicates whether the term is a hypothesis marker.
            </summary>
            <remarks>Mark a hypothesis in a natural deduction style proof.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofLemma">
             <summary>
             Indicates whether the term is a proof by lemma
             </summary>
             <remarks>
             T1: false
             [lemma T1]: (or (not l_1) ... (not l_n))
            
             This proof object has one antecedent: a hypothetical proof for false.
             It converts the proof in a proof for (or (not l_1) ... (not l_n)),
             when T1 contains the hypotheses: l_1, ..., l_n.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofUnitResolution">
            <summary>
            Indicates whether the term is a proof by unit resolution
            </summary>
            <remarks>
            T1:      (or l_1 ... l_n l_1' ... l_m')
            T2:      (not l_1)
            ...
            T(n+1):  (not l_n)
            [unit-resolution T1 ... T(n+1)]: (or l_1' ... l_m')
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofIFFTrue">
            <summary>
            Indicates whether the term is a proof by iff-true
            </summary>
            <remarks>
            T1: p
            [iff-true T1]: (iff p true)
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofIFFFalse">
            <summary>
            Indicates whether the term is a proof by iff-false
            </summary>
            <remarks>
            T1: (not p)
            [iff-false T1]: (iff p false)
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofCommutativity">
             <summary>
             Indicates whether the term is a proof by commutativity
             </summary>
             <remarks>
             [comm]: (= (f a b) (f b a))
            
             f is a commutative operator.
            
             This proof object has no antecedents.
             Remark: if f is bool, then = is iff.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofDefAxiom">
             <summary>
             Indicates whether the term is a proof for Tseitin-like axioms
             </summary>
             <remarks>
             Proof object used to justify Tseitin's like axioms:
            
             (or (not (and p q)) p)
             (or (not (and p q)) q)
             (or (not (and p q r)) p)
             (or (not (and p q r)) q)
             (or (not (and p q r)) r)
             ...
             (or (and p q) (not p) (not q))
             (or (not (or p q)) p q)
             (or (or p q) (not p))
             (or (or p q) (not q))
             (or (not (iff p q)) (not p) q)
             (or (not (iff p q)) p (not q))
             (or (iff p q) (not p) (not q))
             (or (iff p q) p q)
             (or (not (ite a b c)) (not a) b)
             (or (not (ite a b c)) a c)
             (or (ite a b c) (not a) (not b))
             (or (ite a b c) a (not c))
             (or (not (not a)) (not a))
             (or (not a) a)
            
             This proof object has no antecedents.
             Note: all axioms are propositional tautologies.
             Note also that 'and' and 'or' can take multiple arguments.
             You can recover the propositional tautologies by
             unfolding the Boolean connectives in the axioms a small
             bounded number of steps (=3).
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofDefIntro">
             <summary>
             Indicates whether the term is a proof for introduction of a name
             </summary>
             <remarks>
             Introduces a name for a formula/term.
             Suppose e is an expression with free variables x, and def-intro
             introduces the name n(x). The possible cases are:
            
             When e is of Boolean type:
             [def-intro]: (and (or n (not e)) (or (not n) e))
            
             or:
             [def-intro]: (or (not n) e)
             when e only occurs positively.
            
             When e is of the form (ite cond th el):
             [def-intro]: (and (or (not cond) (= n th)) (or cond (= n el)))
            
             Otherwise:
             [def-intro]: (= n e)
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofApplyDef">
            <summary>
            Indicates whether the term is a proof for application of a definition
            </summary>
            <remarks>
             [apply-def T1]: F ~ n
             F is 'equivalent' to n, given that T1 is a proof that
             n is a name for F.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofIFFOEQ">
            <summary>
            Indicates whether the term is a proof iff-oeq
            </summary>
            <remarks>
            T1: (iff p q)
            [iff~ T1]: (~ p q)
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofNNFPos">
             <summary>
             Indicates whether the term is a proof for a positive NNF step
             </summary>
             <remarks>
             Proof for a (positive) NNF step. Example:
            
             T1: (not s_1) ~ r_1
             T2: (not s_2) ~ r_2
             T3: s_1 ~ r_1'
             T4: s_2 ~ r_2'
             [nnf-pos T1 T2 T3 T4]: (~ (iff s_1 s_2)
                                       (and (or r_1 r_2') (or r_1' r_2)))
            
             The negation normal form steps NNF_POS and NNF_NEG are used in the following cases:
             (a) When creating the NNF of a positive force quantifier.
             The quantifier is retained (unless the bound variables are eliminated).
             Example
                T1: q ~ q_new
                [nnf-pos T1]: (~ (forall (x T) q) (forall (x T) q_new))
            
             (b) When recursively creating NNF over Boolean formulas, where the top-level
             connective is changed during NNF conversion. The relevant Boolean connectives
             for NNF_POS are 'implies', 'iff', 'xor', 'ite'.
             NNF_NEG furthermore handles the case where negation is pushed
             over Boolean connectives 'and' and 'or'.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofNNFNeg">
             <summary>
             Indicates whether the term is a proof for a negative NNF step
             </summary>
             <remarks>
             Proof for a (negative) NNF step. Examples:
            
               T1: (not s_1) ~ r_1
               ...
               Tn: (not s_n) ~ r_n
               [nnf-neg T1 ... Tn]: (not (and s_1 ... s_n)) ~ (or r_1 ... r_n)
             and
               T1: (not s_1) ~ r_1
               ...
               Tn: (not s_n) ~ r_n
               [nnf-neg T1 ... Tn]: (not (or s_1 ... s_n)) ~ (and r_1 ... r_n)
             and
               T1: (not s_1) ~ r_1
               T2: (not s_2) ~ r_2
               T3: s_1 ~ r_1'
               T4: s_2 ~ r_2'
               [nnf-neg T1 T2 T3 T4]: (~ (not (iff s_1 s_2))
                                         (and (or r_1 r_2) (or r_1' r_2')))
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofSkolemize">
             <summary>
             Indicates whether the term is a proof for a Skolemization step
             </summary>
             <remarks>
             Proof for:
            
               [sk]: (~ (not (forall x (p x y))) (not (p (sk y) y)))
               [sk]: (~ (exists x (p x y)) (p (sk y) y))
            
             This proof object has no antecedents.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofModusPonensOEQ">
            <summary>
            Indicates whether the term is a proof by modus ponens for equi-satisfiability.
            </summary>
            <remarks>
            Modus ponens style rule for equi-satisfiability.
            T1: p
            T2: (~ p q)
            [mp~ T1 T2]: q
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsProofTheoryLemma">
             <summary>
             Indicates whether the term is a proof for theory lemma
             </summary>
             <remarks>
             Generic proof for theory lemmas.
            
             The theory lemma function comes with one or more parameters.
             The first parameter indicates the name of the theory.
             For the theory of arithmetic, additional parameters provide hints for
             checking the theory lemma.
             The hints for arithmetic are:
             - farkas - followed by rational coefficients. Multiply the coefficients to the
               inequalities in the lemma, add the (negated) inequalities and obtain a contradiction.
             - triangle-eq - Indicates a lemma related to the equivalence:
               (iff (= t1 t2) (and (&lt;= t1 t2) (&lt;= t2 t1)))
             - gcd-test - Indicates an integer linear arithmetic lemma that uses a gcd test.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelation">
            <summary>
            Indicates whether the term is of relation sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationStore">
            <summary>
            Indicates whether the term is an relation store
            </summary>
            <remarks>
            Insert a record into a relation.
            The function takes <c>n+1</c> arguments, where the first argument is the relation and the remaining <c>n</c> elements
            correspond to the <c>n</c> columns of the relation.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsEmptyRelation">
            <summary>
            Indicates whether the term is an empty relation
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsIsEmptyRelation">
            <summary>
            Indicates whether the term is a test for the emptiness of a relation
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationalJoin">
            <summary>
            Indicates whether the term is a relational join
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationUnion">
            <summary>
            Indicates whether the term is the union or convex hull of two relations.
            </summary>
            <remarks>The function takes two arguments.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationWiden">
            <summary>
            Indicates whether the term is the widening of two relations
            </summary>
            <remarks>The function takes two arguments.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationProject">
            <summary>
            Indicates whether the term is a projection of columns (provided as numbers in the parameters).
            </summary>
            <remarks>The function takes one argument.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationFilter">
            <summary>
            Indicates whether the term is a relation filter
            </summary>
            <remarks>
            Filter (restrict) a relation with respect to a predicate.
            The first argument is a relation.
            The second argument is a predicate with free de-Bruijn indices
            corresponding to the columns of the relation.
            So the first column in the relation has index 0.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationNegationFilter">
             <summary>
             Indicates whether the term is an intersection of a relation with the negation of another.
             </summary>
             <remarks>
             Intersect the first relation with respect to negation
             of the second relation (the function takes two arguments).
             Logically, the specification can be described by a function
            
               target = filter_by_negation(pos, neg, columns)
            
             where columns are pairs c1, d1, .., cN, dN of columns from pos and neg, such that
             target are elements in x in pos, such that there is no y in neg that agrees with
             x on the columns c1, d1, .., cN, dN.
             </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationRename">
            <summary>
            Indicates whether the term is the renaming of a column in a relation
            </summary>
            <remarks>
            The function takes one argument.
            The parameters contain the renaming as a cycle.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationComplement">
            <summary>
            Indicates whether the term is the complement of a relation
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationSelect">
            <summary>
            Indicates whether the term is a relational select
            </summary>
            <remarks>
            Check if a record is an element of the relation.
            The function takes <c>n+1</c> arguments, where the first argument is a relation,
            and the remaining <c>n</c> arguments correspond to a record.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsRelationClone">
            <summary>
            Indicates whether the term is a relational clone (copy)
            </summary>
            <remarks>
            Create a fresh copy (clone) of a relation.
            The function is logically the identity, but
            in the context of a register machine allows
            for terms of kind <seealso cref="P:Microsoft.Z3.Expr.IsRelationUnion"/>
            to perform destructive updates to the first argument.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFiniteDomain">
            <summary>
            Indicates whether the term is of an array sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFiniteDomainLT">
            <summary>
            Indicates whether the term is a less than predicate over a finite domain.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFP">
            <summary>
             Indicates whether the terms is of floating-point sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRM">
            <summary>
             Indicates whether the terms is of floating-point rounding mode sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPNumeral">
            <summary>
            Indicates whether the term is a floating-point numeral
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMNumeral">
            <summary>
            Indicates whether the term is a floating-point rounding mode numeral
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMRoundNearestTiesToEven">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToEven
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMRoundNearestTiesToAway">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToAway
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMRoundTowardNegative">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardNegative
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMRoundTowardPositive">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardPositive
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMRoundTowardZero">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardZero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMExprRNE">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToEven
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMExprRNA">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToAway
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMExprRTN">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardNegative
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMExprRTP">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardPositive
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMExprRTZ">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardZero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRMExpr">
            <summary>
            Indicates whether the term is a floating-point rounding mode numeral
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPPlusInfinity">
            <summary>
            Indicates whether the term is a floating-point +oo
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPMinusInfinity">
            <summary>
            Indicates whether the term is a floating-point -oo
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPNaN">
            <summary>
            Indicates whether the term is a floating-point NaN
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPPlusZero">
            <summary>
            Indicates whether the term is a floating-point +zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPMinusZero">
            <summary>
            Indicates whether the term is a floating-point -zero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPAdd">
            <summary>
            Indicates whether the term is a floating-point addition term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPSub">
            <summary>
            Indicates whether the term is a floating-point subtraction term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPNeg">
            <summary>
            Indicates whether the term is a floating-point negation term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPMul">
            <summary>
            Indicates whether the term is a floating-point multiplication term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPDiv">
            <summary>
            Indicates whether the term is a floating-point division term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRem">
            <summary>
            Indicates whether the term is a floating-point remainder term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPAbs">
            <summary>
            Indicates whether the term is a floating-point term absolute value term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPMin">
            <summary>
            Indicates whether the term is a floating-point minimum term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPMax">
            <summary>
            Indicates whether the term is a floating-point maximum term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPFMA">
            <summary>
            Indicates whether the term is a floating-point fused multiply-add term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPSqrt">
            <summary>
            Indicates whether the term is a floating-point square root term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPRoundToIntegral">
            <summary>
            Indicates whether the term is a floating-point roundToIntegral term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPEq">
            <summary>
            Indicates whether the term is a floating-point equality term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPLt">
            <summary>
            Indicates whether the term is a floating-point less-than term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPGt">
            <summary>
            Indicates whether the term is a floating-point greater-than term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPLe">
            <summary>
            Indicates whether the term is a floating-point less-than or equal term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPGe">
            <summary>
            Indicates whether the term is a floating-point greater-than or equal term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisNaN">
            <summary>
            Indicates whether the term is a floating-point isNaN predicate term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisInf">
            <summary>
            Indicates whether the term is a floating-point isInf predicate term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisZero">
            <summary>
            Indicates whether the term is a floating-point isZero predicate term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisNormal">
            <summary>
            Indicates whether the term is a floating-point isNormal term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisSubnormal">
            <summary>
            Indicates whether the term is a floating-point isSubnormal predicate term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisNegative">
            <summary>
            Indicates whether the term is a floating-point isNegative predicate term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPisPositive">
            <summary>
            Indicates whether the term is a floating-point isPositive predicate term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPFP">
            <summary>
            Indicates whether the term is a floating-point constructor term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPToFp">
            <summary>
            Indicates whether the term is a floating-point conversion term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPToFpUnsigned">
            <summary>
            Indicates whether the term is a floating-point conversion from unsigned bit-vector term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPToUBV">
            <summary>
            Indicates whether the term is a floating-point conversion to unsigned bit-vector term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPToSBV">
            <summary>
            Indicates whether the term is a floating-point conversion to signed bit-vector term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPToReal">
            <summary>
            Indicates whether the term is a floating-point conversion to real term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.IsFPToIEEEBV">
            <summary>
            Indicates whether the term is a floating-point conversion to IEEE-754 bit-vector term
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Expr.Index">
            <summary>
            The de-Bruijn index of a bound variable.
            </summary>
            <remarks>
            Bound variables are indexed by de-Bruijn indices. It is perhaps easiest to explain
            the meaning of de-Bruijn indices by indicating the compilation process from
            non-de-Bruijn formulas to de-Bruijn format.
            <code>
            abs(forall (x1) phi) = forall (x1) abs1(phi, x1, 0)
            abs(forall (x1, x2) phi) = abs(forall (x1) abs(forall (x2) phi))
            abs1(x, x, n) = b_n
            abs1(y, x, n) = y
            abs1(f(t1,...,tn), x, n) = f(abs1(t1,x,n), ..., abs1(tn,x,n))
            abs1(forall (x1) phi, x, n) = forall (x1) (abs1(phi, x, n+1))
            </code>
            The last line is significant: the index of a bound variable is different depending
            on the scope in which it appears. The deeper x appears, the higher is its
            index.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Expr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary>
            Constructor for Expr
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FiniteDomainExpr">
            <summary>
            Finite-domain expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FiniteDomainExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for DatatypeExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.FiniteDomainNum">
            <summary>
            Finite-domain numerals
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FiniteDomainNum.UInt64">
            <summary>
            Retrieve the 64-bit unsigned integer value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FiniteDomainNum.Int">
            <summary>
            Retrieve the int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FiniteDomainNum.Int64">
            <summary>
            Retrieve the 64-bit int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FiniteDomainNum.UInt">
            <summary>
            Retrieve the int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FiniteDomainNum.BigInteger">
            <summary>
            Retrieve the BigInteger value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FiniteDomainNum.ToString">
            <summary>
            Returns a string representation of the numeral.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FiniteDomainSort">
            <summary>
            Finite domain sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FiniteDomainSort.Size">
            <summary>
            The size of the finite domain sort.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Fixedpoint">
            <summary>
            Object for managing fixedpoints
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Fixedpoint.Help">
            <summary>
            A string that describes all available fixedpoint solver parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Fixedpoint.Parameters">
            <summary>
            Sets the fixedpoint solver parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Fixedpoint.ParameterDescriptions">
            <summary>
            Retrieves parameter descriptions for Fixedpoint solver.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.Assert(Microsoft.Z3.BoolExpr[])">
            <summary>
            Assert a constraint (or multiple) into the fixedpoint solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.Add(Microsoft.Z3.BoolExpr[])">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.RegisterRelation(Microsoft.Z3.FuncDecl)">
            <summary>
            Register predicate as recursive relation.
            </summary>       
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.AddRule(Microsoft.Z3.BoolExpr,Microsoft.Z3.Symbol)">
            <summary>
            Add rule into the fixedpoint solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.AddFact(Microsoft.Z3.FuncDecl,System.UInt32[])">
            <summary>
            Add table fact to the fixedpoint solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.Query(Microsoft.Z3.BoolExpr)">
            <summary>
            Query the fixedpoint solver.
            A query is a conjunction of constraints. The constraints may include the recursively defined relations.
            The query is satisfiable if there is an instance of the query variables and a derivation for it.
            The query is unsatisfiable if there are no derivations satisfying the query variables. 
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.Query(Microsoft.Z3.FuncDecl[])">
            <summary>
            Query the fixedpoint solver.
            A query is an array of relations.
            The query is satisfiable if there is an instance of some relation that is non-empty.
            The query is unsatisfiable if there are no derivations satisfying any of the relations.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.UpdateRule(Microsoft.Z3.BoolExpr,Microsoft.Z3.Symbol)">
            <summary>
            Update named rule into in the fixedpoint solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.GetAnswer">
            <summary>
            Retrieve satisfying instance or instances of solver, 
            or definitions for the recursive predicates that show unsatisfiability.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.GetReasonUnknown">
            <summary>
            Retrieve explanation why fixedpoint engine returned status Unknown.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.GetNumLevels(Microsoft.Z3.FuncDecl)">
            <summary>
            Retrieve the number of levels explored for a given predicate.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.GetCoverDelta(System.Int32,Microsoft.Z3.FuncDecl)">
            <summary>
            Retrieve the cover of a predicate.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.AddCover(System.Int32,Microsoft.Z3.FuncDecl,Microsoft.Z3.Expr)">
            <summary>
            Add <tt>property</tt> about the <tt>predicate</tt>.
            The property is added at <tt>level</tt>.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.ToString">
            <summary>
            Retrieve internal string representation of fixedpoint object.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.SetPredicateRepresentation(Microsoft.Z3.FuncDecl,Microsoft.Z3.Symbol[])">
            <summary>
            Instrument the Datalog engine on which table representation to use for recursive predicate.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.ToString(Microsoft.Z3.BoolExpr[])">
            <summary>
            Convert benchmark given as set of axioms, rules and queries to a string.
            </summary>                
        </member>
        <member name="P:Microsoft.Z3.Fixedpoint.Rules">
            <summary>
            Retrieve set of rules added to fixedpoint context.
            </summary>                
        </member>
        <member name="P:Microsoft.Z3.Fixedpoint.Assertions">
            <summary>
            Retrieve set of assertions added to fixedpoint context.
            </summary>                
        </member>
        <member name="P:Microsoft.Z3.Fixedpoint.Statistics">
            <summary>
            Fixedpoint statistics.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.ParseFile(System.String)">
            <summary>
            Parse an SMT-LIB2 file with fixedpoint rules. 
            Add the rules to the current fixedpoint context. 
            Return the set of queries in the file.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Fixedpoint.ParseString(System.String)">
            <summary>
            Similar to ParseFile. Instead it takes as argument a string.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FPExpr">
            <summary>
            FloatingPoint Expressions
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPExpr.EBits">
            <summary>
            The number of exponent bits.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPExpr.SBits">
            <summary>
            The number of significand bits.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FPExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for FPExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.FPNum">
            <summary>
            FloatiungPoint Numerals
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.SignBV">
            <summary>
            The sign of a floating-point numeral as a bit-vector expression
            </summary>
            <remarks>
            NaN's do not have a bit-vector sign, so they are invalid arguments.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.FPNum.Sign">
            <summary>
            Retrieves the sign of a floating-point literal
            </summary>
            <remarks>
            Remarks: returns true if the numeral is negative
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.FPNum.Significand">
            <summary>
            The significand value of a floating-point numeral as a string
            </summary>
            <remarks>
            The significand s is always 0 &lt; s &lt; 2.0; the resulting string is long
            enough to represent the real significand precisely.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.FPNum.SignificandUInt64">
            <summary>
            The significand value of a floating-point numeral as a UInt64
            </summary>
            <remarks>
            This function extracts the significand bits, without the
            hidden bit or normalization. Throws an exception if the
            significand does not fit into a UInt64.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.FPNum.SignificandBV">
            <summary>
            The significand of a floating-point numeral as a bit-vector expression
            </summary>
            <remarks>
            +oo, -oo and NaN's do not have a bit-vector significand, so they are invalid arguments.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.FPNum.Exponent(System.Boolean)">
            <summary>
            Return the (biased) exponent value of a floating-point numeral as a string
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FPNum.ExponentInt64(System.Boolean)">
            <summary>
            Return the exponent value of a floating-point numeral as a signed 64-bit integer
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FPNum.ExponentBV(System.Boolean)">
            <summary>
            The exponent of a floating-point numeral as a bit-vector expression
            </summary>
            <remarks>
            +oo, -oo and NaN's do not have a bit-vector exponent, so they are invalid arguments.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsNaN">
            <summary>
            Indicates whether the numeral is a NaN.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsInf">
            <summary>
            Indicates whether the numeral is a +oo or -oo.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsZero">
            <summary>
            Indicates whether the numeral is +zero or -zero.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsNormal">
            <summary>
            Indicates whether the numeral is normal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsSubnormal">
            <summary>
            Indicates whether the numeral is subnormal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsPositive">
            <summary>
            Indicates whether the numeral is positive.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPNum.IsNegative">
            <summary>
            Indicates whether the numeral is negative.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FPNum.ToString">
            <summary>
            Returns a string representation of the numeral.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FPRMExpr">
            <summary>
            FloatingPoint RoundingMode Expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FPRMExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for FPRMExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.FPRMNum">
            <summary>
            Floating-point rounding mode numerals
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRoundNearestTiesToEven">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToEven
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRNE">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToEven
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRoundNearestTiesToAway">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToAway
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRNA">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundNearestTiesToAway
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRoundTowardPositive">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardPositive
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRTP">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardPositive
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRoundTowardNegative">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardNegative
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRTN">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardNegative
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRoundTowardZero">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardZero
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPRMNum.isRTZ">
            <summary>
            Indicates whether the term is the floating-point rounding numeral roundTowardZero
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FPRMNum.ToString">
            <summary>
            Returns a string representation of the numeral.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.FPRMNum.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for FPRMNum </summary>
        </member>
        <member name="T:Microsoft.Z3.FPRMSort">
            <summary>
            The FloatingPoint RoundingMode sort
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FPSort">
            <summary>
            FloatingPoint sort
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPSort.EBits">
            <summary>
            The number of exponent bits.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FPSort.SBits">
            <summary>
            The number of significand bits.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FuncDecl">
            <summary>
            Function declarations.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.op_Equality(Microsoft.Z3.FuncDecl,Microsoft.Z3.FuncDecl)">
            <summary>
            Comparison operator.
            </summary>
            <returns>True if <paramref name="a"/> and <paramref name="b"/> share the same context and are equal, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.op_Inequality(Microsoft.Z3.FuncDecl,Microsoft.Z3.FuncDecl)">
            <summary>
            Comparison operator.
            </summary>
            <returns>True if <paramref name="a"/> and <paramref name="b"/> do not share the same context or are not equal, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.Equals(System.Object)">
            <summary>
            Object comparison.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.GetHashCode">
            <summary>
            A hash code.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.ToString">
            <summary>
            A string representations of the function declaration.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Id">
            <summary>
            Returns a unique identifier for the function declaration.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Arity">
            <summary>
            The arity of the function declaration
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.DomainSize">
            <summary>
            The size of the domain of the function declaration
            <seealso cref="P:Microsoft.Z3.FuncDecl.Arity"/>
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Domain">
            <summary>
            The domain of the function declaration
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Range">
            <summary>
            The range of the function declaration
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.DeclKind">
            <summary>
            The kind of the function declaration.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Name">
            <summary>
            The name of the function declaration
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.NumParameters">
            <summary>
            The number of parameters of the function declaration
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameters">
            <summary>
            The parameters of the function declaration
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FuncDecl.Parameter">
            <summary>
            Function declarations can have Parameters associated with them.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.Int">
            <summary>The int value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.Double">
            <summary>The double value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.Symbol">
            <summary>The Symbol value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.Sort">
            <summary>The Sort value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.AST">
            <summary>The AST value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.FuncDecl">
            <summary>The FunctionDeclaration value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.Rational">
            <summary>The rational string value of the parameter.</summary>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Parameter.ParameterKind">
            <summary>
            The kind of the parameter.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the function declaration to the Context <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A copy of the function declaration which is associated with <paramref name="ctx"/></returns>
        </member>
        <member name="P:Microsoft.Z3.FuncDecl.Item(Microsoft.Z3.Expr[])">
            <summary>
            Create expression that applies function to arguments.
            </summary>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.FuncDecl.Apply(Microsoft.Z3.Expr[])">
            <summary>
            Create expression that applies function to arguments.
            </summary>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Z3.FuncInterp">
            <summary>
             A function interpretation is represented as a finite map and an 'else' value.
             Each entry in the finite map represents the value of a function given a set of arguments.  
            </summary>
        </member>
        <member name="T:Microsoft.Z3.FuncInterp.Entry">
            <summary>
            An Entry object represents an element in the finite map used to encode
            a function interpretation.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.Entry.Value">
            <summary>
            Return the (symbolic) value of this entry.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.Entry.NumArgs">
            <summary>
            The number of arguments of the entry.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.Entry.Args">
            <summary>
            The arguments of the function entry.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FuncInterp.Entry.ToString">
            <summary>
            A string representation of the function entry.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.NumEntries">
            <summary>
            The number of entries in the function interpretation.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.Entries">
            <summary>
            The entries in the function interpretation
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.Else">
            <summary>
            The (symbolic) `else' value of the function interpretation.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.FuncInterp.Arity">
            <summary>
            The arity of the function interpretation
            </summary>
        </member>
        <member name="M:Microsoft.Z3.FuncInterp.ToString">
            <summary>
            A string representation of the function interpretation.
            </summary>    
        </member>
        <member name="T:Microsoft.Z3.Global">
            <summary>
            Global functions for Z3. 
            </summary>
            <remarks>
            This (static) class contains functions that effect the behaviour of Z3
            globally across contexts, etc. 
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Global.SetParameter(System.String,System.String)">
            <summary>
            Set a global (or module) parameter, which is shared by all Z3 contexts.
            </summary>
            <remarks>
            When a Z3 module is initialized it will use the value of these parameters
            when Z3_params objects are not provided.
            The name of parameter can be composed of characters [a-z][A-Z], digits [0-9], '-' and '_'. 
            The character '.' is a delimiter (more later).
            The parameter names are case-insensitive. The character '-' should be viewed as an "alias" for '_'.
            Thus, the following parameter names are considered equivalent: "pp.decimal-precision"  and "PP.DECIMAL_PRECISION".
            This function can be used to set parameters for a specific Z3 module.
            This can be done by using [module-name].[parameter-name].
            For example:
            Z3_global_param_set('pp.decimal', 'true')
            will set the parameter "decimal" in the module "pp" to true.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Global.GetParameter(System.String)">
            <summary>
            Get a global (or module) parameter.
            </summary>
            <remarks>               
            Returns null if the parameter <param name="id"/> does not exist.
            The caller must invoke #Z3_global_param_del_value to delete the value returned at \c param_value.
            This function cannot be invoked simultaneously from different threads without synchronization.
            The result string stored in param_value is stored in a shared location.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Global.ResetParameters">
            <summary>
            Restore the value of all global (and module) parameters.
            </summary>
            <remarks>
            This command will not affect already created objects (such as tactics and solvers)
            </remarks>
            <seealso cref="M:Microsoft.Z3.Global.SetParameter(System.String,System.String)"/>
        </member>
        <member name="M:Microsoft.Z3.Global.ToggleWarningMessages(System.Boolean)">
            <summary>
            Enable/disable printing of warning messages to the console.
            </summary>
            <remarks>Note that this function is static and effects the behaviour of 
            all contexts globally.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Global.EnableTrace(System.String)">
            <summary>
            Enable tracing messages tagged as `tag' when Z3 is compiled in debug mode.
            </summary>
            <remarks>
            It is a NOOP otherwise. 
            </remarks>
            <param name="tag">trace tag</param>
        </member>
        <member name="M:Microsoft.Z3.Global.DisableTrace(System.String)">
            <summary>
            Disable tracing messages tagged as `tag' when Z3 is compiled in debug mode.        
            </summary>
            <remarks>
            It is a NOOP otherwise.
            </remarks>
            <param name="tag">trace tag</param>
        </member>
        <member name="T:Microsoft.Z3.Goal">
            <summary>
            A goal (aka problem). A goal is essentially a set
            of formulas, that can be solved and/or transformed using
            tactics and solvers.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.Precision">
            <summary>
            The precision of the goal. 
            </summary>
            <remarks>
            Goals can be transformed using over and under approximations.
            An under approximation is applied when the objective is to find a model for a given goal.
            An over approximation is applied when the objective is to find a proof for a given goal.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Goal.IsPrecise">
            <summary>
            Indicates whether the goal is precise.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.IsUnderApproximation">
            <summary>
            Indicates whether the goal is an under-approximation.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.IsOverApproximation">
            <summary>
            Indicates whether the goal is an over-approximation.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.IsGarbage">
            <summary>
            Indicates whether the goal is garbage (i.e., the product of over- and under-approximations).
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Goal.Assert(Microsoft.Z3.BoolExpr[])">
            <summary>
            Adds the <paramref name="constraints"/> to the given goal. 
            </summary>   
        </member>
        <member name="M:Microsoft.Z3.Goal.Add(Microsoft.Z3.BoolExpr[])">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="P:Microsoft.Z3.Goal.Inconsistent">
            <summary>
            Indicates whether the goal contains `false'.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.Depth">
            <summary>
            The depth of the goal.
            </summary>
            <remarks>
            This tracks how many transformations were applied to it.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Goal.Reset">
            <summary>
            Erases all formulas from the given goal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.Size">
            <summary>
            The number of formulas in the goal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.Formulas">
            <summary>
            The formulas in the goal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.NumExprs">
            <summary>
            The number of formulas, subformulas and terms in the goal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.IsDecidedSat">
            <summary>
            Indicates whether the goal is empty, and it is precise or the product of an under approximation.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Goal.IsDecidedUnsat">
            <summary>
            Indicates whether the goal contains `false', and it is precise or the product of an over approximation.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Goal.ConvertModel(Microsoft.Z3.Model)">
            <summary>
            Convert a model for the goal into a model of the
            original goal from which this goal was derived.
            </summary>
            <returns>A model for <c>g</c></returns>
        </member>
        <member name="M:Microsoft.Z3.Goal.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the Goal to the target Context <paramref name="ctx"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Goal.Simplify(Microsoft.Z3.Params)">
            <summary>
            Simplifies the goal.
            </summary>
            <remarks>Essentially invokes the `simplify' tactic on the goal.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Goal.ToString">
            <summary>
            Goal to string conversion.
            </summary>
            <returns>A string representation of the Goal.</returns>
        </member>
        <member name="M:Microsoft.Z3.Goal.ToDimacs(System.Boolean)">
            <summary>
            Goal to DIMACS formatted string conversion.
            </summary>
            <returns>A string representation of the Goal.</returns>
        </member>
        <member name="M:Microsoft.Z3.Goal.AsBoolExpr">
            <summary>
            Goal to BoolExpr conversion.
            </summary>
            <returns>A string representation of the Goal.</returns>
        </member>
        <member name="T:Microsoft.Z3.IntExpr">
            <summary>
            Int expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.IntExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for IntExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.IntNum">
            <summary>
            Integer Numerals
            </summary>
        </member>
        <member name="P:Microsoft.Z3.IntNum.UInt64">
            <summary>
            Retrieve the 64-bit unsigned integer value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.IntNum.Int">
            <summary>
            Retrieve the int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.IntNum.Int64">
            <summary>
            Retrieve the 64-bit int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.IntNum.UInt">
            <summary>
            Retrieve the int value.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.IntNum.BigInteger">
            <summary>
            Retrieve the BigInteger value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.IntNum.ToString">
            <summary>
            Returns a string representation of the numeral.
            </summary>        
        </member>
        <member name="T:Microsoft.Z3.IntSort">
            <summary>
             An Integer sort
            </summary>
        </member>
        <member name="T:Microsoft.Z3.IntSymbol">
            <summary>
            Numbered symbols
            </summary>
        </member>
        <member name="P:Microsoft.Z3.IntSymbol.Int">
            <summary>
            The int value of the symbol.
            </summary>
            <remarks>Throws an exception if the symbol is not of int kind. </remarks>
        </member>
        <member name="T:Microsoft.Z3.Lambda">
            <summary>
            Lambda expressions.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Lambda.NumBound">
            <summary>
            The number of bound variables.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Lambda.BoundVariableNames">
            <summary>
            The symbols for the bound variables.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Lambda.BoundVariableSorts">
            <summary>
            The sorts of the bound variables.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Lambda.Body">
            <summary>
            The body of the lambda.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Lambda.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the lambda to the Context <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A copy of the lambda which is associated with <paramref name="ctx"/></returns>
        </member>
        <member name="T:Microsoft.Z3.ListSort">
            <summary>
            List sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ListSort.NilDecl">
            <summary>
            The declaration of the nil function of this list sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ListSort.Nil">
            <summary>
            The empty list.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ListSort.IsNilDecl">
            <summary>
            The declaration of the isNil function of this list sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ListSort.ConsDecl">
            <summary>
            The declaration of the cons function of this list sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ListSort.IsConsDecl">
            <summary>
            The declaration of the isCons function of this list sort.
            </summary>
            
        </member>
        <member name="P:Microsoft.Z3.ListSort.HeadDecl">
            <summary>
            The declaration of the head function of this list sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ListSort.TailDecl">
            <summary>
            The declaration of the tail function of this list sort.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Log">
            <summary>
            Interaction logging for Z3.
            </summary>
            <remarks>
            Note that this is a global, static log and if multiple Context 
            objects are created, it logs the interaction with all of them.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Log.Open(System.String)">
            <summary>
            Open an interaction log file.
            </summary>
            <param name="filename">the name of the file to open</param>
            <returns>True if opening the log file succeeds, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.Log.Close">
            <summary>
            Closes the interaction log.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Log.Append(System.String)">
            <summary>
            Appends the user-provided string <paramref name="s"/> to the interaction log.
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.Log.isOpen">
            <summary>
            Checks whether the interaction log is opened.
            </summary>
            <returns>True if the interaction log is open, false otherwise.</returns>
        </member>
        <member name="T:Microsoft.Z3.Model">
            <summary>
            A Model contains interpretations (assignments) of constants and functions. 
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Model.ConstInterp(Microsoft.Z3.Expr)">
            <summary>
            Retrieves the interpretation (the assignment) of <paramref name="a"/> in the model. 
            </summary>
            <param name="a">A Constant</param>
            <returns>An expression if the constant has an interpretation in the model, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.Model.ConstInterp(Microsoft.Z3.FuncDecl)">
            <summary>
            Retrieves the interpretation (the assignment) of <paramref name="f"/> in the model. 
            </summary>
            <param name="f">A function declaration of zero arity</param>
            <returns>An expression if the function has an interpretation in the model, null otherwise.</returns>    
        </member>
        <member name="M:Microsoft.Z3.Model.FuncInterp(Microsoft.Z3.FuncDecl)">
            <summary>
            Retrieves the interpretation (the assignment) of a non-constant <paramref name="f"/> in the model. 
            </summary>
            <param name="f">A function declaration of non-zero arity</param>
            <returns>A FunctionInterpretation if the function has an interpretation in the model, null otherwise.</returns> 
        </member>
        <member name="P:Microsoft.Z3.Model.NumConsts">
            <summary>
            The number of constants that have an interpretation in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Model.ConstDecls">
            <summary>
            The function declarations of the constants in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Model.Consts">
            <summary>
            Enumerate constants in model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Model.NumFuncs">
            <summary>
            The number of function interpretations in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Model.FuncDecls">
            <summary>
            The function declarations of the function interpretations in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Model.Decls">
            <summary>
            All symbols that have an interpretation in the model.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Model.ModelEvaluationFailedException">
            <summary>
            A ModelEvaluationFailedException is thrown when an expression cannot be evaluated by the model.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Model.ModelEvaluationFailedException.#ctor">
            <summary>
            An exception that is thrown when model evaluation fails.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Model.Eval(Microsoft.Z3.Expr,System.Boolean)">
            <summary>
            Evaluates the expression <paramref name="t"/> in the current model.
            </summary>
            <remarks>
            This function may fail if <paramref name="t"/> contains quantifiers, 
            is partial (MODEL_PARTIAL enabled), or if <paramref name="t"/> is not well-sorted.
            In this case a <c>ModelEvaluationFailedException</c> is thrown.
            </remarks>
            <param name="t">An expression</param>
            <param name="completion">
            When this flag is enabled, a model value will be assigned to any constant 
            or function that does not have an interpretation in the model.
            </param>
            <returns>The evaluation of <paramref name="t"/> in the model.</returns>        
        </member>
        <member name="M:Microsoft.Z3.Model.Evaluate(Microsoft.Z3.Expr,System.Boolean)">
            <summary>
            Alias for <c>Eval</c>.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Model.Double(Microsoft.Z3.Expr)">
            <summary>
            Evaluate expression to a double, assuming it is a numeral already.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Model.NumSorts">
            <summary>
            The number of uninterpreted sorts that the model has an interpretation for.
            </summary>    
        </member>
        <member name="P:Microsoft.Z3.Model.Sorts">
            <summary>
            The uninterpreted sorts that the model has an interpretation for. 
            </summary>
            <remarks>
            Z3 also provides an interpretation for uninterpreted sorts used in a formula.
            The interpretation for a sort is a finite set of distinct values. We say this finite set is
            the "universe" of the sort.
            </remarks>
            <seealso cref="P:Microsoft.Z3.Model.NumSorts"/>
            <seealso cref="M:Microsoft.Z3.Model.SortUniverse(Microsoft.Z3.Sort)"/>
        </member>
        <member name="M:Microsoft.Z3.Model.SortUniverse(Microsoft.Z3.Sort)">
            <summary>
            The finite set of distinct values that represent the interpretation for sort <paramref name="s"/>.
            </summary>
            <seealso cref="P:Microsoft.Z3.Model.Sorts"/>
            <param name="s">An uninterpreted sort</param>
            <returns>An array of expressions, where each is an element of the universe of <paramref name="s"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Model.ToString">
            <summary>
            Conversion of models to strings. 
            </summary>
            <returns>A string representation of the model.</returns>
        </member>
        <member name="T:Microsoft.Z3.NativeContext">
            <summary>
            The main interaction with Z3 happens via the Context.
            NativeContext allows for efficient wrapper-reduced interaction with Z3
            expressions.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.#ctor(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Constructor.
            </summary>
            <remarks>
            The following parameters can be set:
                - proof  (Boolean)           Enable proof generation
                - debug_ref_count (Boolean)  Enable debug support for Z3_ast reference counting
                - trace  (Boolean)           Tracing support for VCC
                - trace_file_name (String)   Trace out file for VCC traces
                - timeout (unsigned)         default timeout (in milliseconds) used for solvers
                - well_sorted_check          type checker
                - auto_config                use heuristics to automatically select solver and configure it
                - model                      model generation for solvers, this parameter can be overwritten when creating a solver
                - model_validate             validate models produced by solvers
                - unsat_core                 unsat-core generation for solvers, this parameter can be overwritten when creating a solver
            Note that in previous versions of Z3, this constructor was also used to set global and module parameters.
            For this purpose we should now use <see cref="M:Microsoft.Z3.Global.SetParameter(System.String,System.String)"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkAdd(System.IntPtr[])">
            <summary>
            Create an expression representing <c>t[0] + t[1] + ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkMul(System.IntPtr[])">
            <summary>
            Create an expression representing <c>t[0] * t[1] * ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkSub(System.IntPtr[])">
            <summary>
            Create an expression representing <c>t[0] - t[1] - ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkDiv(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 / t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkLe(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 &lt;= t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkLt(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 &lt; t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkGe(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 &gt;= t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkGt(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 &gt; t2</c>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvUlt(System.IntPtr,System.IntPtr)">
            <summary>
            Unsigned less-than
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvUle(System.IntPtr,System.IntPtr)">
            <summary>
            Unsigned less-than-equal
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkEq(System.IntPtr,System.IntPtr)">
            <summary>
            Creates the equality <paramref name="x"/> = <paramref name="y"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkNot(System.IntPtr)">
            <summary>
             Mk an expression representing <c>not(a)</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkAnd(System.IntPtr[])">
            <summary>
            Create an expression representing <c>t[0] and t[1] and ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkOr(System.IntPtr[])">
            <summary>
            Create an expression representing <c>t[0] or t[1] or ...</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkReal(System.String)">
            <summary>
            Create a real numeral.
            </summary>
            <param name="v">A string representing the Term value in decimal notation.</param>
            <returns>A Term with value <paramref name="v"/> and sort Real</returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkNumeral(System.Int32,System.IntPtr)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="sort">Sort of the numeral</param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkNumeral(System.UInt32,System.IntPtr)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="sort">Sort of the numeral</param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkNumeral(System.Int64,System.IntPtr)">
            <summary>
            Create a Term of a given sort. This function can be used to create numerals that fit in a machine integer.
            </summary>
            <param name="v">Value of the numeral</param>
            <param name="sort">Sort of the numeral</param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkIte(System.IntPtr,System.IntPtr,System.IntPtr)">
            <summary>
             Create an expression representing an if-then-else: <c>ite(t1, t2, t3)</c>.
            </summary>
            <param name="t1">An expression with Boolean sort</param>
            <param name="t2">An expression </param>
            <param name="t3">An expression with the same sort as <paramref name="t2"/></param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkImplies(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 -> t2</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeContext.IntSort">
            <summary>
            Integer Sort
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeContext.BoolSort">
            <summary>
            Returns the "singleton" BoolSort for this NativeContext
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeContext.RealSort">
            <summary>
            Returns the "singleton" RealSort for this NativeContext
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvSort(System.UInt32)">
            <summary>
            Returns the BvSort for size in this NativeContext
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkListSort(System.String,System.IntPtr,System.IntPtr@,System.IntPtr@,System.IntPtr@,System.IntPtr@,System.IntPtr@,System.IntPtr@)">
            <summary>
            returns ListSort
            </summary>
            <param name="name"></param>
            <param name="elemSort"></param>
            <param name="inil"></param>
            <param name="iisnil"></param>
            <param name="icons"></param>
            <param name="iiscons"></param>
            <param name="ihead"></param>
            <param name="itail"></param>
            <returns>The list algebraic datatype</returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkArraySort(System.IntPtr,System.IntPtr)">
            <summary>
            Create a new array sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkTupleSort(System.IntPtr,System.IntPtr[],System.IntPtr[],System.IntPtr@,System.IntPtr[])">
            <summary>
            Create a new tuple sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkTrue">
            <summary>
            The true Term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkFalse">
            <summary>
            The false Term.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBool(System.Boolean)">
            <summary>
            Creates a Boolean value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkIff(System.IntPtr,System.IntPtr)">
            <summary>
            Create an expression representing <c>t1 iff t2</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkConst(System.String,System.IntPtr)">
            <summary>
            Creates a new Constant of sort <paramref name="range"/> and named <paramref name="name"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkStringSymbol(System.String)">
            <summary>
            Return a ptr to symbol for string
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkApp(System.IntPtr,System.IntPtr[])">
            <summary>
            Create a new function application.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBound(System.UInt32,System.IntPtr)">
            <summary>
            Creates a new bound variable.
            </summary>
            <param name="index">The de-Bruijn index of the variable</param>
            <param name="sort">The sort of the variable</param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvAnd(System.IntPtr,System.IntPtr)">
            <summary>
            Bitwise conjunction.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvNot(System.IntPtr)">
            <summary>
            Bitwise negation.
            </summary>
            <remarks>The argument must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvNeg(System.IntPtr)">
            <summary>
            Standard two's complement unary minus.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBVNeg(System.IntPtr)">
            <summary>
            Standard two's complement unary minus.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvAdd(System.IntPtr,System.IntPtr)">
            <summary>
            Two's complement addition.
            </summary>
            <remarks>The arguments must have the same bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvExtract(System.UInt32,System.UInt32,System.IntPtr)">
            <summary>
            Bit-vector extraction.
            </summary>
            <remarks>
            Extract the bits <paramref name="high"/> down to <paramref name="low"/> from a bitvector of
            size <c>m</c> to yield a new bitvector of size <c>n</c>, where
            <c>n = high - low + 1</c>.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvSignExt(System.UInt32,System.IntPtr)">
            <summary>
            Bit-vector sign extension.
            </summary>
            <remarks>
            Sign-extends the given bit-vector to the (signed) equivalent bitvector of
            size <c>m+i</c>, where \c m is the size of the given bit-vector.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvZeroExt(System.UInt32,System.IntPtr)">
            <summary>
            Bit-vector zero extension.
            </summary>
            <remarks>
            Extend the given bit-vector with zeros to the (unsigned) equivalent
            bitvector of size <c>m+i</c>, where \c m is the size of the
            given bit-vector.
            The argument <paramref name="t"/> must have a bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvShl(System.IntPtr,System.IntPtr)">
             <summary>
             Shift left.
             </summary>
             <remarks>
             It is equivalent to multiplication by <c>2^x</c> where \c x is the value of <paramref name="t2"/>.
            
             NB. The semantics of shift operations varies between environments. This
             definition does not necessarily capture directly the semantics of the
             programming language or assembly architecture you are modeling.
            
             The arguments must have a bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvLshr(System.IntPtr,System.IntPtr)">
             <summary>
             Logical shift right
             </summary>
             <remarks>
             It is equivalent to unsigned division by <c>2^x</c> where \c x is the value of <paramref name="t2"/>.
            
             NB. The semantics of shift operations varies between environments. This
             definition does not necessarily capture directly the semantics of the
             programming language or assembly architecture you are modeling.
            
             The arguments must have a bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvAshr(System.IntPtr,System.IntPtr)">
             <summary>
             Arithmetic shift right
             </summary>
             <remarks>
             It is like logical shift right except that the most significant
             bits of the result always copy the most significant bit of the
             second argument.
            
             NB. The semantics of shift operations varies between environments. This
             definition does not necessarily capture directly the semantics of the
             programming language or assembly architecture you are modeling.
            
             The arguments must have a bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvSlt(System.IntPtr,System.IntPtr)">
            <summary>
            Two's complement signed less-than
            </summary>
            <remarks>
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvMul(System.IntPtr,System.IntPtr)">
            <summary>
            Two's complement multiplication.
            </summary>
            <remarks>The arguments must have the same bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvUdiv(System.IntPtr,System.IntPtr)">
            <summary>
            Unsigned division.
            </summary>
            <remarks>
            It is defined as the floor of <c>t1/t2</c> if \c t2 is
            different from zero. If <c>t2</c> is zero, then the result
            is undefined.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvSdiv(System.IntPtr,System.IntPtr)">
             <summary>
             Signed division.
             </summary>
             <remarks>
             It is defined in the following way:
            
             - The \c floor of <c>t1/t2</c> if \c t2 is different from zero, and <c>t1*t2 >= 0</c>.
            
             - The \c ceiling of <c>t1/t2</c> if \c t2 is different from zero, and <c>t1*t2 &lt; 0</c>.
            
             If <c>t2</c> is zero, then the result is undefined.
             The arguments must have the same bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvUrem(System.IntPtr,System.IntPtr)">
            <summary>
            Unsigned remainder.
            </summary>
            <remarks>
            It is defined as <c>t1 - (t1 /u t2) * t2</c>, where <c>/u</c> represents unsigned division.
            If <c>t2</c> is zero, then the result is undefined.
            The arguments must have the same bit-vector sort.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvSrem(System.IntPtr,System.IntPtr)">
             <summary>
             Signed remainder.
             </summary>
             <remarks>
             It is defined as <c>t1 - (t1 /s t2) * t2</c>, where <c>/s</c> represents signed division.
             The most significant bit (sign) of the result is equal to the most significant bit of \c t1.
            
             If <c>t2</c> is zero, then the result is undefined.
             The arguments must have the same bit-vector sort.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvSub(System.IntPtr,System.IntPtr)">
            <summary>
            Two's complement subtraction.
            </summary>
            <remarks>The arguments must have the same bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvOr(System.IntPtr,System.IntPtr)">
            <summary>
            Bitwise disjunction.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkBvXor(System.IntPtr,System.IntPtr)">
            <summary>
            Bitwise XOR.
            </summary>
            <remarks>The arguments must have a bit-vector sort.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkForall(System.IntPtr[],System.IntPtr[],System.IntPtr,System.UInt32,System.IntPtr[],System.IntPtr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create a universal Quantifier.
            </summary>
            <remarks>
            Creates a forall formula, where <paramref name="weight"/> is the weight,
            <paramref name="patterns"/> is an array of patterns, <paramref name="sorts"/> is an array
            with the sorts of the bound variables, <paramref name="names"/> is an array with the
            'names' of the bound variables, and <paramref name="body"/> is the body of the
            quantifier. Quantifiers are associated with weights indicating the importance of
            using the quantifier during instantiation.
            Note that the bound variables are de-Bruijn indices created using <see cref="M:Microsoft.Z3.NativeContext.MkBound(System.UInt32,System.IntPtr)"/>.
            Z3 applies the convention that the last element in <paramref name="names"/> and
            <paramref name="sorts"/> refers to the variable with index 0, the second to last element
            of <paramref name="names"/> and <paramref name="sorts"/> refers to the variable
            with index 1, etc.
            </remarks>
            <param name="sorts">the sorts of the bound variables.</param>
            <param name="names">names of the bound variables</param>
            <param name="body">the body of the quantifier.</param>
            <param name="weight">quantifiers are associated with weights indicating the importance of using the quantifier during instantiation. By default, pass the weight 0.</param>
            <param name="patterns">array containing the patterns created using <c>MkPattern</c>.</param>
            <param name="noPatterns">array containing the anti-patterns created using <c>MkPattern</c>.</param>
            <param name="quantifierID">optional symbol to track quantifier.</param>
            <param name="skolemID">optional symbol to track skolem constants.</param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkExists(System.IntPtr[],System.IntPtr[],System.IntPtr,System.UInt32,System.IntPtr[],System.IntPtr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Same as MkForAll but defaults to "forall" = false
            Create an existential Quantifier.
            </summary>
            <param name="sorts"></param>
            <param name="names"></param>
            <param name="body"></param>
            <param name="weight"></param>
            <param name="patterns"></param>
            <param name="noPatterns"></param>
            <param name="quantifierID"></param>
            <param name="skolemID"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkQuantifier(System.Boolean,System.IntPtr[],System.IntPtr[],System.IntPtr,System.UInt32,System.IntPtr[],System.IntPtr[],Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Create a quantified expression either forall or exists
            </summary>
            <param name="is_forall"></param>
            <param name="sorts"></param>
            <param name="names"></param>
            <param name="body"></param>
            <param name="weight"></param>
            <param name="patterns"></param>
            <param name="noPatterns"></param>
            <param name="quantifierID"></param>
            <param name="skolemID"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Z3.NativeContext.PrintMode">
            <summary>
            Selects the format used for pretty-printing expressions.
            </summary>
            <remarks>
            The default mode for pretty printing expressions is to produce
            SMT-LIB style output where common subexpressions are printed
            at each occurrence. The mode is called Z3_PRINT_SMTLIB_FULL.
            To print shared common subexpressions only once,
            use the Z3_PRINT_LOW_LEVEL mode.
            To print in way that conforms to SMT-LIB standards and uses let
            expressions to share common sub-expressions use Z3_PRINT_SMTLIB_COMPLIANT.
            </remarks>
            <seealso cref="M:Microsoft.Z3.AST.ToString"/>
            <seealso cref="M:Microsoft.Z3.Pattern.ToString"/>
            <seealso cref="M:Microsoft.Z3.FuncDecl.ToString"/>
            <seealso cref="M:Microsoft.Z3.Sort.ToString"/>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkConstArray(System.IntPtr,System.IntPtr)">
            <summary>
            Create a constant array.
            </summary>
            <remarks>
            The resulting term is an array, such that a <c>select</c>on an arbitrary index
            produces the value <c>v</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkStore(System.IntPtr,System.IntPtr,System.IntPtr)">
            <summary>
            Array update.
            </summary>
            <remarks>
            The node <c>a</c> must have an array sort <c>[domain -> range]</c>,
            <c>i</c> must have sort <c>domain</c>,
            <c>v</c> must have sort range. The sort of the result is <c>[domain -> range]</c>.
            The semantics of this function is given by the theory of arrays described in the SMT-LIB
            standard. See http://smtlib.org for more details.
            The result of this function is an array that is equal to <c>a</c>
            (with respect to <c>select</c>)
            on all indices except for <c>i</c>, where it maps to <c>v</c>
            (and the <c>select</c> of <c>a</c> with
            respect to <c>i</c> may be a different value).
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkSelect(System.IntPtr,System.IntPtr)">
             <summary>
             Array read.
             </summary>
             <remarks>
             The argument <c>array</c> is the array and <c>index</c> is the index
             of the array that gets read.
            
             The node <c>array</c> must have an array sort <c>[domain -> range]</c>,
             and <c>index</c> must have the sort <c>domain</c>.
             The sort of the result is <c>range</c>.
             </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkDefault(System.IntPtr)">
            <summary>
            Access the array default value.
            </summary>
            <remarks>
            Produces the default range value, for arrays that can be represented as
            finite maps with a default range value.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkFuncDecl(System.String,System.IntPtr[],System.IntPtr)">
            <summary>
            Creates a new function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkFuncDecl(System.String,System.IntPtr,System.IntPtr)">
            <summary>
            Creates a new function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkFreshFuncDecl(System.String,System.IntPtr[],System.IntPtr)">
            <summary>
            Creates a fresh function declaration with a name prefixed with <paramref name="prefix"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkConstDecl(System.String,System.IntPtr)">
            <summary>
            Creates a new constant function declaration.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetDomain(System.IntPtr)">
            <summary>
            Get domain for a funcdecl
            </summary>
            <param name="fdecl"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetRange(System.IntPtr)">
            <summary>
            Get range for a funcdecl
            </summary>
            <param name="fdecl"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkPattern(System.IntPtr[])">
            <summary>
            Create a quantifier pattern.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.MkSimpleSolver">
            <summary>
            Creates a new (incremental) solver.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetSortKind(System.IntPtr)">
            <summary>
            Get the sort kind from IntPtr
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetAstKind(System.IntPtr)">
            <summary>
            Get the AST kind from IntPtr
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetDeclKind(System.IntPtr)">
            <summary>
            Get the Decl kind from IntPtr
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetSort(System.IntPtr)">
            <summary>
            Get Sort for AST
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetAppArgs(System.IntPtr)">
            <summary>
            Get the arguments for app
            </summary>
            <param name="app"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetNumArgs(System.IntPtr)">
            <summary>
            Return number of arguments for app
            </summary>
            <param name="app"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetAppDecl(System.IntPtr)">
            <summary>
            Get App Decl from IntPtr
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetDeclName(System.IntPtr)">
            <summary>
            Get string name for Decl
            </summary>
            <param name="decl"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetBvSortSize(System.IntPtr)">
            <summary>
            Get size of BitVector Sort
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetArraySortDomain(System.IntPtr)">
            <summary>
            Get the domain IntPtr for Sort
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetArraySortRange(System.IntPtr)">
            <summary>
            Get the range IntPtr for Sort
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.TryGetNumeralInt(System.IntPtr,System.Int32@)">
            <summary>
            Try to get integer from AST
            </summary>
            <param name="v"></param>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.TryGetNumeralUInt(System.IntPtr,System.UInt32@)">
            <summary>
            Try to get uint from AST
            </summary>
            <param name="v"></param>
            <param name="u"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.TryGetNumeralInt64(System.IntPtr,System.Int64@)">
            <summary>
            Try to get long from AST
            </summary>
            <param name="v"></param>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.TryGetNumeralUInt64(System.IntPtr,System.UInt64@)">
            <summary>
            Try get ulong from AST
            </summary>
            <param name="v"></param>
            <param name="u"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetNumeralString(System.IntPtr)">
            <summary>
            Get string for numeral ast
            </summary>
            <param name="v"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.ToString(System.IntPtr)">
            <summary>
            Get printable string representing Z3_ast
            </summary>
            <param name="ast"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.ToggleWarningMessages(System.Boolean)">
            <summary>
            Enable or disable warning messages
            </summary>
            <param name="turnOn"></param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.EnableTrace(System.String)">
            <summary>
            Enable trace to file
            </summary>
            <param name="tag">Tag to trace</param>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.Dispose">
            <summary>
            Disposes of the context.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.ToArray(System.IntPtr)">
            <summary>
            Utility to convert a vector object of ast to a .Net array
            </summary>
            <param name="vec"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.NativeContext.GetStatistics(System.IntPtr)">
            <summary>
            Retrieve statistics as an array of entries
            </summary>
            <param name="stats"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Z3.NativeFuncInterp">
            <summary>
             A function interpretation is represented as a finite map and an 'else' value.
             Each entry in the finite map represents the value of a function given a set of arguments.  
            </summary>
        </member>
        <member name="T:Microsoft.Z3.NativeFuncInterp.Entry">
            <summary>
            Evaluation entry of a function
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeFuncInterp.Entry.Arguments">
            <summary>
            Argument values that define entry
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeFuncInterp.Entry.Result">
            <summary>
            Result of applying function to Arguments in the interpretation
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeFuncInterp.Declaration">
            <summary>
            Function that is interpreted
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeFuncInterp.Entries">
            <summary>
            Set of non-default entries defining the function graph
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeFuncInterp.Else">
            <summary>
            Default cause of the function interpretation
            </summary>
        </member>
        <member name="T:Microsoft.Z3.NativeModel">
            <summary>
            A Model contains interpretations (assignments) of constants and functions. 
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.ConstInterp(System.IntPtr)">
            <summary>
            Retrieves the interpretation (the assignment) of <paramref name="a"/> in the model. 
            </summary>
            <param name="a">A Constant</param>
            <returns>An expression if the constant has an interpretation in the model, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.ConstFuncInterp(System.IntPtr)">
            <summary>
            Retrieves the interpretation (the assignment) of <paramref name="f"/> in the model. 
            </summary>
            <param name="f">A function declaration of zero arity</param>
            <returns>An expression if the function has an interpretation in the model, null otherwise.</returns>    
        </member>
        <member name="M:Microsoft.Z3.NativeModel.FuncInterp(System.IntPtr)">
            <summary>
            Retrieves the interpretation (the assignment) of a non-constant <paramref name="f"/> in the model. 
            </summary>
            <param name="f">A function declaration of non-zero arity</param>
            <returns>A FunctionInterpretation if the function has an interpretation in the model, null otherwise.</returns> 
        </member>
        <member name="P:Microsoft.Z3.NativeModel.NumConsts">
            <summary>
            The number of constants that have an interpretation in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeModel.ConstDecls">
            <summary>
            The function declarations of the constants in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeModel.Consts">
            <summary>
            Enumerate constants in model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeModel.NumFuncs">
            <summary>
            The number of function interpretations in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeModel.FuncDecls">
            <summary>
            The function declarations of the function interpretations in the model.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeModel.Decls">
            <summary>
            All symbols that have an interpretation in the model.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.NativeModel.ModelEvaluationFailedException">
            <summary>
            A ModelEvaluationFailedException is thrown when an expression cannot be evaluated by the model.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.ModelEvaluationFailedException.#ctor">
            <summary>
            An exception that is thrown when model evaluation fails.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.Eval(System.IntPtr,System.Boolean)">
            <summary>
            Evaluates the expression <paramref name="t"/> in the current model.
            </summary>
            <remarks>
            This function may fail if <paramref name="t"/> contains quantifiers, 
            is partial (MODEL_PARTIAL enabled), or if <paramref name="t"/> is not well-sorted.
            In this case a <c>ModelEvaluationFailedException</c> is thrown.
            </remarks>
            <param name="t">An expression</param>
            <param name="completion">
            When this flag is enabled, a model value will be assigned to any constant 
            or function that does not have an interpretation in the model.
            </param>
            <returns>The evaluation of <paramref name="t"/> in the model.</returns>        
        </member>
        <member name="M:Microsoft.Z3.NativeModel.Evaluate(System.IntPtr,System.Boolean)">
            <summary>
            Alias for <c>Eval</c>.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.NativeModel.Double(System.IntPtr)">
            <summary>
            Evaluate expression to a double, assuming it is a numeral already.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.NativeModel.ArrayValue">
            <summary>
            An array value obtained by untangling a model assignment.
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeModel.ArrayValue.Updates">
            <summary>
            One dimensional array of indices where the array is updated
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeModel.ArrayValue.Else">
            <summary>
            default Else case
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeModel.ArrayValue.Domain">
            <summary>
            Domain for array
            Updates.Keys
            </summary>
        </member>
        <member name="F:Microsoft.Z3.NativeModel.ArrayValue.Range">
            <summary>
            Range for array
            Updates.Values
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.TryGetArrayValue(System.IntPtr,Microsoft.Z3.NativeModel.ArrayValue@)">
            <summary>
            Convert the interpretation of t into a sequence of array updates
            </summary>
            <param name="t"></param>
            <param name="result"></param>
            <returns>null if the argument does evaluate to a sequence of stores to an array</returns>
        </member>
        <member name="P:Microsoft.Z3.NativeModel.NumSorts">
            <summary>
            The number of uninterpreted sorts that the model has an interpretation for.
            </summary>    
        </member>
        <member name="P:Microsoft.Z3.NativeModel.Sorts">
            <summary>
            The uninterpreted sorts that the model has an interpretation for. 
            </summary>
            <remarks>
            Z3 also provides an interpretation for uninterpreted sorts used in a formula.
            The interpretation for a sort is a finite set of distinct values. We say this finite set is
            the "universe" of the sort.
            </remarks>
            <seealso cref="P:Microsoft.Z3.NativeModel.NumSorts"/>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.ToString">
            <summary>
            Conversion of models to strings. 
            </summary>
            <returns>A string representation of the model.</returns>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.Finalize">
            <summary>
            Finalizer.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeModel.Dispose">
            <summary>
            Disposes of the underlying native Z3 object.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.NativeSolver">
            <summary>
            Solvers.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.Help">
            <summary>
            A string that describes all available solver parameters.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Set(System.String,System.Boolean)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Set(System.String,System.UInt32)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Set(System.String,System.Double)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Set(System.String,System.String)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.NumScopes">
            <summary>
            The current number of backtracking points (scopes).
            </summary>
            <seealso cref="M:Microsoft.Z3.NativeSolver.Pop(System.UInt32)"/>
            <seealso cref="M:Microsoft.Z3.NativeSolver.Push"/>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Push">
            <summary>
            Creates a backtracking point.
            </summary>
            <seealso cref="M:Microsoft.Z3.NativeSolver.Pop(System.UInt32)"/>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Pop(System.UInt32)">
            <summary>
            Backtracks <paramref name="n"/> backtracking points.
            </summary>
            <remarks>Note that an exception is thrown if <paramref name="n"/> is not smaller than <c>NumScopes</c></remarks>
            <seealso cref="M:Microsoft.Z3.NativeSolver.Push"/>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Reset">
            <summary>
            Resets the Solver.
            </summary>
            <remarks>This removes all assertions from the solver.</remarks>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Assert(System.IntPtr[])">
            <summary>
            Assert a constraint (or multiple) into the solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Add(System.IntPtr[])">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Add(System.Collections.Generic.IEnumerable{System.IntPtr})">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.AssertInjective(System.IntPtr)">
            <summary>
            Add constraints to ensure the function f can only be injective.
            Example:
            for function f : D1 x D2 -> R
            assert axioms
              forall (x1 : D1, x2 : D2) x1 = inv1(f(x1,x2))
              forall (x1 : D1, x2 : D2) x2 = inv2(f(x1,x2))
            </summary>
            <param name="f"></param>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.AssertAndTrack(System.IntPtr[],System.IntPtr[])">
            <summary>
            Assert multiple constraints into the solver, and track them (in the unsat) core 
            using the Boolean constants in ps. 
            </summary>
            <remarks>
            This API is an alternative to <see cref="M:Microsoft.Z3.NativeSolver.Check(System.IntPtr[])"/> with assumptions for extracting unsat cores.
            Both APIs can be used in the same solver. The unsat core will contain a combination
            of the Boolean variables provided using <see cref="M:Microsoft.Z3.NativeSolver.AssertAndTrack(System.IntPtr[],System.IntPtr[])"/> 
            and the Boolean literals
            provided using <see cref="M:Microsoft.Z3.NativeSolver.Check(System.IntPtr[])"/> with assumptions.
            </remarks>        
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.AssertAndTrack(System.IntPtr,System.IntPtr)">
            <summary>
            Assert a constraint into the solver, and track it (in the unsat) core 
            using the Boolean constant p. 
            </summary>
            <remarks>
            This API is an alternative to <see cref="M:Microsoft.Z3.NativeSolver.Check(System.IntPtr[])"/> with assumptions for extracting unsat cores.
            Both APIs can be used in the same solver. The unsat core will contain a combination
            of the Boolean variables provided using <see cref="M:Microsoft.Z3.NativeSolver.AssertAndTrack(System.IntPtr[],System.IntPtr[])"/> 
            and the Boolean literals
            provided using <see cref="M:Microsoft.Z3.NativeSolver.Check(System.IntPtr[])"/> with assumptions.
            </remarks>        
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.FromFile(System.String)">
            <summary>
            Load solver assertions from a file.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.FromString(System.String)">
            <summary>
            Load solver assertions from a string.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.NumAssertions">
            <summary>
            The number of assertions in the solver.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.Assertions">
            <summary>
            The set of asserted formulas.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.Units">
            <summary>
            Currently inferred units.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Check(System.IntPtr[])">
            <summary>
            Checks whether the assertions in the solver are consistent or not.
            </summary>
            <remarks>
            <seealso cref="P:Microsoft.Z3.NativeSolver.Model"/>
            <seealso cref="P:Microsoft.Z3.NativeSolver.UnsatCore"/>
            <seealso cref="P:Microsoft.Z3.NativeSolver.Proof"/>    
            </remarks>    
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Check(System.Collections.Generic.IEnumerable{System.IntPtr})">
            <summary>
            Checks whether the assertions in the solver are consistent or not.
            </summary>
            <remarks>
            <seealso cref="P:Microsoft.Z3.NativeSolver.Model"/>
            <seealso cref="P:Microsoft.Z3.NativeSolver.UnsatCore"/>
            <seealso cref="P:Microsoft.Z3.NativeSolver.Proof"/>    
            </remarks>    
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.Model">
            <summary>
            The model of the last <c>Check(params Expr[] assumptions)</c>.
            </summary>
            <remarks>
            The result is <c>null</c> if <c>Check(params Expr[] assumptions)</c> was not invoked before,
            if its results was not <c>SATISFIABLE</c>, or if model production is not enabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.Proof">
            <summary>
            The proof of the last <c>Check(params Expr[] assumptions)</c>.
            </summary>
            <remarks>    
            The result is <c>null</c> if <c>Check(params Expr[] assumptions)</c> was not invoked before,
            if its results was not <c>UNSATISFIABLE</c>, or if proof production is disabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.UnsatCore">
            <summary>
            The unsat core of the last <c>Check</c>.
            </summary>
            <remarks>
            The unsat core is a subset of <c>Assertions</c>
            The result is empty if <c>Check</c> was not invoked before,
            if its results was not <c>UNSATISFIABLE</c>, or if core production is disabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.ReasonUnknown">
            <summary>
            A brief justification of why the last call to <c>Check</c> returned <c>UNKNOWN</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Translate(Microsoft.Z3.NativeContext)">
            <summary>
            Create a clone of the current solver with respect to <c>ctx</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.ImportModelConverter(Microsoft.Z3.NativeSolver)">
            <summary>
            Import model converter from other solver. 
            </summary>
        </member>
        <member name="P:Microsoft.Z3.NativeSolver.Statistics">
            <summary>
            Solver statistics.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.ToString">
            <summary>
            A string representation of the solver.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Finalize">
            <summary>
            Finalizer.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.NativeSolver.Dispose">
            <summary>
            Disposes of the underlying native Z3 object.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.OnClause">
            <summary>
            OnClause - clause inference callback
            </summary>        
        </member>
        <member name="T:Microsoft.Z3.OnClause.OnClauseEh">
            <summary>
            Delegate type for when clauses are inferred.
            An inference is a pair comprising of
            - a proof (hint). A partial (or comprehensive) derivation justifying the inference.
            - a clause (vector of literals)	
            The life-time of the proof hint and clause vector is limited to the scope of the callback.
            should the callback want to store hints or clauses it will need to call Dup on the hints
            and/or extract literals from the clause, respectively.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.OnClause.#ctor(Microsoft.Z3.Solver,Microsoft.Z3.OnClause.OnClauseEh)">
            <summary>
            OnClause constructor
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.OnClause.Finalize">
            <summary>
            Release private memory.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.OnClause.Dispose">
            <summary>
            Must be called. The object will not be garbage collected automatically even if the context is disposed
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Optimize">
            <summary>
            Object for managing optimization context
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Help">
            <summary>
            A string that describes all available optimize solver parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Parameters">
            <summary>
            Sets the optimize solver parameters.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(System.String,System.Boolean)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(System.String,System.UInt32)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(System.String,System.Double)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(System.String,System.String)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(System.String,Microsoft.Z3.Symbol)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(Microsoft.Z3.Symbol,System.Boolean)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(Microsoft.Z3.Symbol,System.UInt32)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(Microsoft.Z3.Symbol,System.Double)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(Microsoft.Z3.Symbol,System.String)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Set(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Sets parameter on the optimize solver
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.ParameterDescriptions">
            <summary>
            Retrieves parameter descriptions for Optimize solver.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Assert(Microsoft.Z3.BoolExpr[])">
            <summary>
            Assert a constraint (or multiple) into the optimize solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Optimize.Assert(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Assert a constraint (or multiple) into the optimize solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Optimize.Add(Microsoft.Z3.BoolExpr[])">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Optimize.Add(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Optimize.AddConstraints(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Assert a constraint (or multiple) into the optimize solver.
            </summary>   
        </member>
        <member name="M:Microsoft.Z3.Optimize.AssertAndTrack(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary>
            Assert a constraint into the optimize solver, and track it (in the unsat) core 
            using the Boolean constant p. 
            </summary>
            <remarks>
            This API is an alternative to <see cref="M:Microsoft.Z3.Optimize.Check(Microsoft.Z3.Expr[])"/> with assumptions for extracting unsat cores.
            Both APIs can be used in the same solver. The unsat core will contain a combination
            of the Boolean variables provided using <see cref="!:AssertAndTrack(BoolExpr[],BoolExpr[])"/> 
            and the Boolean literals
            provided using <see cref="M:Microsoft.Z3.Optimize.Check(Microsoft.Z3.Expr[])"/> with assumptions.
            </remarks>        
        </member>
        <member name="T:Microsoft.Z3.Optimize.Handle">
            <summary>
            Handle to objectives returned by objective functions.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Handle.Lower">
            <summary>
            Retrieve a lower bound for the objective handle.
            </summary>                   
        </member>
        <member name="P:Microsoft.Z3.Optimize.Handle.Upper">
            <summary>
            Retrieve an upper bound for the objective handle.
            </summary>                   
        </member>
        <member name="P:Microsoft.Z3.Optimize.Handle.Value">
            <summary>
            Retrieve the value of an objective.
            </summary>                   
        </member>
        <member name="P:Microsoft.Z3.Optimize.Handle.LowerAsVector">
            <summary>
            Retrieve a lower bound for the objective handle.
            </summary>                   
        </member>
        <member name="P:Microsoft.Z3.Optimize.Handle.UpperAsVector">
            <summary>
            Retrieve an upper bound for the objective handle.
            </summary>                   
        </member>
        <member name="M:Microsoft.Z3.Optimize.AssertSoft(Microsoft.Z3.BoolExpr,System.UInt32,System.String)">
            <summary>
            Assert soft constraint
            </summary>        
            <remarks>
            Return an objective which associates with the group of constraints.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Check(Microsoft.Z3.Expr[])">
             <summary>
             Check satisfiability of asserted constraints.
             Produce a model that (when the objectives are bounded and 
             don't use strict inequalities) is optimal.
             </summary>
            
        </member>
        <member name="M:Microsoft.Z3.Optimize.Push">
            <summary>
            Creates a backtracking point.
            </summary>
            <seealso cref="M:Microsoft.Z3.Optimize.Pop"/>
        </member>
        <member name="M:Microsoft.Z3.Optimize.Pop">
            <summary>
            Backtrack one backtracking point.
            </summary>
            <remarks>Note that an exception is thrown if Pop is called without a corresponding <c>Push</c></remarks>
            <seealso cref="M:Microsoft.Z3.Optimize.Push"/>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Model">
            <summary>
            The model of the last <c>Check</c>.
            </summary>
            <remarks>
            The result is <c>null</c> if <c>Check</c> was not invoked before,
            if its results was not <c>SATISFIABLE</c>, or if model production is not enabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Optimize.UnsatCore">
            <summary>
            The unsat core of the last <c>Check</c>.
            </summary>
            <remarks>
            The unsat core is a subset of <c>assumptions</c>
            The result is empty if <c>Check</c> was not invoked before,
            if its results was not <c>UNSATISFIABLE</c>, or if core production is disabled.
            </remarks>
        </member>
        <member name="M:Microsoft.Z3.Optimize.MkMaximize(Microsoft.Z3.Expr)">
            <summary>
            Declare an arithmetical maximization objective.
            Return a handle to the objective. The handle is used as
            to retrieve the values of objectives after calling Check.
            The expression can be either an arithmetical expression or bit-vector.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.Optimize.MkMinimize(Microsoft.Z3.Expr)">
            <summary>
            Declare an arithmetical minimization objective. 
            Similar to MkMaximize.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.Optimize.GetLower(System.UInt32)">
            <summary>
            Retrieve a lower bound for the objective handle.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.Optimize.GetUpper(System.UInt32)">
            <summary>
            Retrieve an upper bound for the objective handle.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.Optimize.GetLowerAsVector(System.UInt32)">
            <summary>
            Retrieve a lower bound for the objective handle.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.Optimize.GetUpperAsVector(System.UInt32)">
            <summary>
            Retrieve an upper bound for the objective handle.
            </summary>            
        </member>
        <member name="P:Microsoft.Z3.Optimize.ReasonUnknown">
            <summary>
            Return a string the describes why the last to check returned unknown
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.Optimize.ToString">
            <summary>
            Print the context to a string (SMT-LIB parseable benchmark).
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.Optimize.FromFile(System.String)">
            <summary>
            Parse an SMT-LIB2 file with optimization objectives and constraints.
            The parsed constraints and objectives are added to the optimization context.
            </summary>                
        </member>
        <member name="M:Microsoft.Z3.Optimize.FromString(System.String)">
            <summary>
            Similar to FromFile. Instead it takes as argument a string.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Assertions">
            <summary>
            The set of asserted formulas.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Objectives">
            <summary>
            The set of asserted formulas.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Optimize.Statistics">
            <summary>
            Optimize statistics.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ParamDescrs">
            <summary>
            A ParamDescrs describes a set of parameters.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ParamDescrs.Validate(Microsoft.Z3.Params)">
            <summary>
            validate a set of parameters.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ParamDescrs.GetKind(Microsoft.Z3.Symbol)">
            <summary>
            Retrieve kind of parameter.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ParamDescrs.GetDocumentation(Microsoft.Z3.Symbol)">
            <summary>
            Retrieve documentation of parameter.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ParamDescrs.Names">
            <summary>
            Retrieve all names of parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.ParamDescrs.Size">
            <summary>
            The size of the ParamDescrs.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ParamDescrs.ToString">
            <summary>
            Retrieves a string representation of the ParamDescrs. 
            </summary>    
        </member>
        <member name="T:Microsoft.Z3.Params">
            <summary>
            A Params objects represents a configuration in the form of Symbol/value pairs.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(Microsoft.Z3.Symbol,System.Boolean)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(Microsoft.Z3.Symbol,System.UInt32)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(Microsoft.Z3.Symbol,System.Double)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(Microsoft.Z3.Symbol,System.String)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(System.String,System.Boolean)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(System.String,System.UInt32)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(System.String,System.Double)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(System.String,Microsoft.Z3.Symbol)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.Add(System.String,System.String)">
            <summary>
            Adds a parameter setting.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Params.ToString">
            <summary>
            A string representation of the parameter set.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Pattern">
            <summary>
            Patterns comprise a list of terms. The list should be
            non-empty.  If the list comprises of more than one term, it is
            also called a multi-pattern.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Pattern.NumTerms">
            <summary>
            The number of terms in the pattern.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Pattern.Terms">
            <summary>
            The terms in the pattern.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Pattern.ToString">
            <summary>
            A string representation of the pattern.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Probe">
            <summary>  
            Probes are used to inspect a goal (aka problem) and collect information that may be used to decide
            which solver and/or preprocessing step will be used.
            The complete list of probes may be obtained using the procedures <c>Context.NumProbes</c>
            and <c>Context.ProbeNames</c>.
            It may also be obtained using the command <c>(help-tactic)</c> in the SMT 2.0 front-end.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Probe.Apply(Microsoft.Z3.Goal)">
            <summary>
            Execute the probe over the goal. 
            </summary>
            <returns>A probe always produce a double value.
            "Boolean" probes return 0.0 for false, and a value different from 0.0 for true.</returns>
        </member>
        <member name="P:Microsoft.Z3.Probe.Item(Microsoft.Z3.Goal)">
            <summary>
            Apply the probe to a goal.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Quantifier">
            <summary>
            Quantifier expressions.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.IsUniversal">
            <summary>
            Indicates whether the quantifier is universal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.IsExistential">
            <summary>
            Indicates whether the quantifier is existential.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.Weight">
            <summary>
            The weight of the quantifier.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.NumPatterns">
            <summary>
            The number of patterns.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.Patterns">
            <summary>
            The patterns.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.NumNoPatterns">
            <summary>
            The number of no-patterns.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.NoPatterns">
            <summary>
            The no-patterns.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.NumBound">
            <summary>
            The number of bound variables.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.BoundVariableNames">
            <summary>
            The symbols for the bound variables.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.BoundVariableSorts">
            <summary>
            The sorts of the bound variables.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Quantifier.Body">
            <summary>
            The body of the quantifier.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Quantifier.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the quantifier to the Context <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A copy of the quantifier which is associated with <paramref name="ctx"/></returns>
        </member>
        <member name="T:Microsoft.Z3.RatNum">
            <summary>
            Rational Numerals
            </summary>
        </member>
        <member name="P:Microsoft.Z3.RatNum.Numerator">
            <summary>
            The numerator of a rational numeral.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.RatNum.Denominator">
            <summary>
            The denominator of a rational numeral.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.RatNum.BigIntNumerator">
            <summary>
            Converts the numerator of the rational to a BigInteger
            </summary>
        </member>
        <member name="P:Microsoft.Z3.RatNum.BigIntDenominator">
            <summary>
            Converts the denominator of the rational to a BigInteger
            </summary>
        </member>
        <member name="M:Microsoft.Z3.RatNum.ToDecimalString(System.UInt32)">
            <summary>
            Returns a string representation in decimal notation.
            </summary>
            <remarks>The result has at most <paramref name="precision"/> decimal places.</remarks>    
        </member>
        <member name="P:Microsoft.Z3.RatNum.Double">
            <summary>
            Returns a double representing the value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.RatNum.ToString">
            <summary>
            Returns a string representation of the numeral.
            </summary>        
        </member>
        <member name="T:Microsoft.Z3.RealExpr">
            <summary>
            Real expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.RealExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for RealExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.RealSort">
            <summary>
            A real sort
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ReExpr">
            <summary>
            Regular expression expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.ReExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for ReExpr </summary>
        </member>
        <member name="T:Microsoft.Z3.RelationSort">
            <summary>
            Relation sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.RelationSort.Arity">
            <summary>
            The arity of the relation sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.RelationSort.ColumnSorts">
            <summary>
            The sorts of the columns of the relation sort.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.ReSort">
            <summary>
             A regular expression sort
            </summary>
        </member>
        <member name="T:Microsoft.Z3.SeqExpr">
            <summary>
            Sequence expressions
            </summary>
        </member>
        <member name="M:Microsoft.Z3.SeqExpr.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary> Constructor for SeqExpr </summary>
        </member>
        <member name="P:Microsoft.Z3.SeqExpr.Item(Microsoft.Z3.Expr)">
            <summary> Access the nth element of a sequence </summary>
        </member>
        <member name="T:Microsoft.Z3.SeqSort">
            <summary>
             A Sequence sort
            </summary>
        </member>
        <member name="T:Microsoft.Z3.SetSort">
            <summary>
            Set sorts.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Simplifier">
            <summary>
            Simplifiers are the basic building block for creating custom solvers with incremental pre-processing.
            The complete list of simplifiers may be obtained using <c>Context.NumSimplifiers</c> 
            and <c>Context.SimplifierNames</c>.
            It may also be obtained using the command <c>(help-simplifier)</c> in the SMT 2.0 front-end.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Simplifier.Help">
            <summary>
            A string containing a description of parameters accepted by the tactic.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Simplifier.ParameterDescriptions">
            <summary>
            Retrieves parameter descriptions for Simplifiers.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Solver">
            <summary>
            Solvers.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.Help">
            <summary>
            A string that describes all available solver parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.Parameters">
            <summary>
            Sets the solver parameters.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(System.String,System.Boolean)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(System.String,System.UInt32)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(System.String,System.Double)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(System.String,System.String)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(System.String,Microsoft.Z3.Symbol)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(Microsoft.Z3.Symbol,System.Boolean)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(Microsoft.Z3.Symbol,System.UInt32)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(Microsoft.Z3.Symbol,System.Double)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(Microsoft.Z3.Symbol,System.String)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Set(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Sets parameter on the solver
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.ParameterDescriptions">
            <summary>
            Retrieves parameter descriptions for solver.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.NumScopes">
            <summary>
            The current number of backtracking points (scopes).
            </summary>
            <seealso cref="M:Microsoft.Z3.Solver.Pop(System.UInt32)"/>
            <seealso cref="M:Microsoft.Z3.Solver.Push"/>
        </member>
        <member name="M:Microsoft.Z3.Solver.Push">
            <summary>
            Creates a backtracking point.
            </summary>
            <seealso cref="M:Microsoft.Z3.Solver.Pop(System.UInt32)"/>
        </member>
        <member name="M:Microsoft.Z3.Solver.Pop(System.UInt32)">
            <summary>
            Backtracks <paramref name="n"/> backtracking points.
            </summary>
            <remarks>Note that an exception is thrown if <paramref name="n"/> is not smaller than <c>NumScopes</c></remarks>
            <seealso cref="M:Microsoft.Z3.Solver.Push"/>
        </member>
        <member name="M:Microsoft.Z3.Solver.Reset">
            <summary>
            Resets the Solver.
            </summary>
            <remarks>This removes all assertions from the solver.</remarks>
        </member>
        <member name="M:Microsoft.Z3.Solver.Assert(Microsoft.Z3.BoolExpr[])">
            <summary>
            Assert a constraint (or multiple) into the solver.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Solver.Add(Microsoft.Z3.BoolExpr[])">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Solver.Add(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Alias for Assert.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.Solver.AssertAndTrack(Microsoft.Z3.BoolExpr[],Microsoft.Z3.BoolExpr[])">
            <summary>
            Assert multiple constraints into the solver, and track them (in the unsat) core 
            using the Boolean constants in ps. 
            </summary>
            <remarks>
            This API is an alternative to <see cref="M:Microsoft.Z3.Solver.Check(Microsoft.Z3.Expr[])"/> with assumptions for extracting unsat cores.
            Both APIs can be used in the same solver. The unsat core will contain a combination
            of the Boolean variables provided using <see cref="M:Microsoft.Z3.Solver.AssertAndTrack(Microsoft.Z3.BoolExpr[],Microsoft.Z3.BoolExpr[])"/> 
            and the Boolean literals
            provided using <see cref="M:Microsoft.Z3.Solver.Check(Microsoft.Z3.Expr[])"/> with assumptions.
            </remarks>        
        </member>
        <member name="M:Microsoft.Z3.Solver.AssertAndTrack(Microsoft.Z3.BoolExpr,Microsoft.Z3.BoolExpr)">
            <summary>
            Assert a constraint into the solver, and track it (in the unsat) core 
            using the Boolean constant p. 
            </summary>
            <remarks>
            This API is an alternative to <see cref="M:Microsoft.Z3.Solver.Check(Microsoft.Z3.Expr[])"/> with assumptions for extracting unsat cores.
            Both APIs can be used in the same solver. The unsat core will contain a combination
            of the Boolean variables provided using <see cref="M:Microsoft.Z3.Solver.AssertAndTrack(Microsoft.Z3.BoolExpr[],Microsoft.Z3.BoolExpr[])"/> 
            and the Boolean literals
            provided using <see cref="M:Microsoft.Z3.Solver.Check(Microsoft.Z3.Expr[])"/> with assumptions.
            </remarks>        
        </member>
        <member name="M:Microsoft.Z3.Solver.FromFile(System.String)">
            <summary>
            Load solver assertions from a file.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.FromString(System.String)">
            <summary>
            Load solver assertions from a string.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.NumAssertions">
            <summary>
            The number of assertions in the solver.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.Assertions">
            <summary>
            The set of asserted formulas.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.Units">
            <summary>
            Currently inferred units.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Check(Microsoft.Z3.Expr[])">
            <summary>
            Checks whether the assertions in the solver are consistent or not.
            </summary>
            <remarks>
            <seealso cref="P:Microsoft.Z3.Solver.Model"/>
            <seealso cref="P:Microsoft.Z3.Solver.UnsatCore"/>
            <seealso cref="P:Microsoft.Z3.Solver.Proof"/>    
            </remarks>    
        </member>
        <member name="M:Microsoft.Z3.Solver.Check(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr})">
            <summary>
            Checks whether the assertions in the solver are consistent or not.
            </summary>
            <remarks>
            <seealso cref="P:Microsoft.Z3.Solver.Model"/>
            <seealso cref="P:Microsoft.Z3.Solver.UnsatCore"/>
            <seealso cref="P:Microsoft.Z3.Solver.Proof"/>    
            </remarks>    
        </member>
        <member name="M:Microsoft.Z3.Solver.Consequences(System.Collections.Generic.IEnumerable{Microsoft.Z3.BoolExpr},System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr},Microsoft.Z3.BoolExpr[]@)">
             <summary>
             Retrieve fixed assignments to the set of variables in the form of consequences.
             Each consequence is an implication of the form 
            
                   relevant-assumptions Implies variable = value
             
             where the relevant assumptions is a subset of the assumptions that are passed in
             and the equality on the right side of the implication indicates how a variable
             is fixed.
             </summary>
             <remarks>
             <seealso cref="P:Microsoft.Z3.Solver.Model"/>
             <seealso cref="P:Microsoft.Z3.Solver.UnsatCore"/>
             <seealso cref="P:Microsoft.Z3.Solver.Proof"/>    
             </remarks>    
        </member>
        <member name="P:Microsoft.Z3.Solver.Model">
            <summary>
            The model of the last <c>Check(params Expr[] assumptions)</c>.
            </summary>
            <remarks>
            The result is <c>null</c> if <c>Check(params Expr[] assumptions)</c> was not invoked before,
            if its results was not <c>SATISFIABLE</c>, or if model production is not enabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Solver.Proof">
            <summary>
            The proof of the last <c>Check(params Expr[] assumptions)</c>.
            </summary>
            <remarks>    
            The result is <c>null</c> if <c>Check(params Expr[] assumptions)</c> was not invoked before,
            if its results was not <c>UNSATISFIABLE</c>, or if proof production is disabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Solver.UnsatCore">
            <summary>
            The unsat core of the last <c>Check</c>.
            </summary>
            <remarks>
            The unsat core is a subset of <c>Assertions</c>
            The result is empty if <c>Check</c> was not invoked before,
            if its results was not <c>UNSATISFIABLE</c>, or if core production is disabled.
            </remarks>
        </member>
        <member name="P:Microsoft.Z3.Solver.ReasonUnknown">
            <summary>
            A brief justification of why the last call to <c>Check</c> returned <c>UNKNOWN</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.BacktrackLevel">
            <summary>
            Backtrack level that can be adjusted by conquer process
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.CubeVariables">
            <summary>
            Variables available and returned by the cuber.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Cube">
            <summary>
            Return a set of cubes.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.Translate(Microsoft.Z3.Context)">
            <summary>
            Create a clone of the current solver with respect to <c>ctx</c>.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.ImportModelConverter(Microsoft.Z3.Solver)">
            <summary>
            Import model converter from other solver. 
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Solver.Statistics">
            <summary>
            Solver statistics.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Solver.ToString">
            <summary>
            A string representation of the solver.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Sort">
            <summary>
            The Sort class implements type information for ASTs.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Sort.op_Equality(Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Comparison operator.
            </summary>
            <param name="a">A Sort</param>
            <param name="b">A Sort</param>
            <returns>True if <paramref name="a"/> and <paramref name="b"/> are from the same context
            and represent the same sort; false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.Sort.op_Inequality(Microsoft.Z3.Sort,Microsoft.Z3.Sort)">
            <summary>
            Comparison operator.
            </summary>
            <param name="a">A Sort</param>
            <param name="b">A Sort</param>
            <returns>True if <paramref name="a"/> and <paramref name="b"/> are not from the same context
            or represent different sorts; false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Z3.Sort.Equals(System.Object)">
            <summary>
            Equality operator for objects of type Sort.
            </summary>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Z3.Sort.GetHashCode">
            <summary>
            Hash code generation for Sorts
            </summary>
            <returns>A hash code</returns>
        </member>
        <member name="P:Microsoft.Z3.Sort.Id">
            <summary>
            Returns a unique identifier for the sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Sort.SortKind">
            <summary>
            The kind of the sort.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Sort.Name">
            <summary>
            The name of the sort
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Sort.ToString">
            <summary>
            A string representation of the sort.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Sort.Translate(Microsoft.Z3.Context)">
            <summary>
            Translates (copies) the sort to the Context <paramref name="ctx"/>.
            </summary>
            <param name="ctx">A context</param>
            <returns>A copy of the sort which is associated with <paramref name="ctx"/></returns>
        </member>
        <member name="M:Microsoft.Z3.Sort.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary>
            Sort constructor
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Statistics">
            <summary>
            Objects of this class track statistical information about solvers. 
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Statistics.Entry">
            <summary>
            Statistical data is organized into pairs of [Key, Entry], where every
            Entry is either a <c>DoubleEntry</c> or a <c>UIntEntry</c>
            </summary>
        </member>
        <member name="F:Microsoft.Z3.Statistics.Entry.Key">
            <summary>
            The key of the entry.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Entry.UIntValue">
            <summary>
            The uint-value of the entry.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Entry.DoubleValue">
            <summary>
            The double-value of the entry.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Entry.IsUInt">
            <summary>
            True if the entry is uint-valued.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Entry.IsDouble">
            <summary>
            True if the entry is double-valued.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Entry.Value">
            <summary>
            The string representation of the entry's value.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Statistics.Entry.ToString">
            <summary>
            The string representation of the Entry.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Statistics.ToString">
            <summary>
            A string representation of the statistical data.
            </summary>    
        </member>
        <member name="P:Microsoft.Z3.Statistics.Size">
            <summary>
            The number of statistical data.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Entries">
            <summary>
            The data entries.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Keys">
            <summary>
            The statistical counters.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Statistics.Item(System.String)">
            <summary>
            The value of a particular statistical counter.
            </summary>        
            <remarks>Returns null if the key is unknown.</remarks>
        </member>
        <member name="T:Microsoft.Z3.Status">
            <summary>
            Status values.
            </summary>
        </member>
        <member name="F:Microsoft.Z3.Status.UNSATISFIABLE">
            <summary>
            Used to signify an unsatisfiable status.
            </summary>
        </member>
        <member name="F:Microsoft.Z3.Status.UNKNOWN">
            <summary>
            Used to signify an unknown status.
            </summary>
        </member>
        <member name="F:Microsoft.Z3.Status.SATISFIABLE">
            <summary>
            Used to signify a satisfiable status.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.StringSymbol">
            <summary>
            Named symbols
            </summary>
        </member>
        <member name="P:Microsoft.Z3.StringSymbol.String">
            <summary>
            The string value of the symbol.
            </summary>
            <remarks>Throws an exception if the symbol is not of string kind.</remarks>
        </member>
        <member name="T:Microsoft.Z3.Symbol">
            <summary>
            Symbols are used to name several term and type constructors.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Symbol.Kind">
            <summary>
            The kind of the symbol (int or string)
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Symbol.IsIntSymbol">
            <summary>
            Indicates whether the symbol is of Int kind
            </summary>    
        </member>
        <member name="M:Microsoft.Z3.Symbol.IsStringSymbol">
            <summary>
            Indicates whether the symbol is of string kind.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Symbol.ToString">
            <summary>
            A string representation of the symbol.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Symbol.op_Equality(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Equality overloading.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Symbol.op_Inequality(Microsoft.Z3.Symbol,Microsoft.Z3.Symbol)">
            <summary>
            Equality overloading.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Symbol.Equals(System.Object)">
            <summary>
            Object comparison.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Symbol.GetHashCode">
            <summary>
            The Symbol's hash code.
            </summary>
            <returns>A hash code</returns>
        </member>
        <member name="M:Microsoft.Z3.Symbol.#ctor(Microsoft.Z3.Context,System.IntPtr)">
            <summary>
            Symbol constructor
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Tactic">
            <summary>
            Tactics are the basic building block for creating custom solvers for specific problem domains.
            The complete list of tactics may be obtained using <c>Context.NumTactics</c> 
            and <c>Context.TacticNames</c>.
            It may also be obtained using the command <c>(help-tactic)</c> in the SMT 2.0 front-end.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Tactic.Help">
            <summary>
            A string containing a description of parameters accepted by the tactic.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Tactic.ParameterDescriptions">
            <summary>
            Retrieves parameter descriptions for Tactics.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Tactic.Apply(Microsoft.Z3.Goal,Microsoft.Z3.Params)">
            <summary>
            Execute the tactic over the goal. 
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Tactic.Item(Microsoft.Z3.Goal)">
            <summary>
            Apply the tactic to a goal.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Tactic.Solver">
            <summary>
            Creates a solver that is implemented using the given tactic.
            </summary>
            <seealso cref="M:Microsoft.Z3.Context.MkSolver(Microsoft.Z3.Tactic)"/>
        </member>
        <member name="T:Microsoft.Z3.TupleSort">
            <summary>
            Tuple sorts.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.TupleSort.MkDecl">
            <summary>
            The constructor function of the tuple.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.TupleSort.NumFields">
            <summary>
            The number of fields in the tuple.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.TupleSort.FieldDecls">
            <summary>
            The field declarations.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.UninterpretedSort">
            <summary>
            Uninterpreted Sorts
            </summary>
        </member>
        <member name="T:Microsoft.Z3.UserPropagator">
            <summary>
            Propagator context for .Net
            </summary>        
        </member>
        <member name="T:Microsoft.Z3.UserPropagator.FixedEh">
            <summary>
            Delegate type for fixed callback
            Note that the life-time of the term and value only applies within the scope of the callback.
            That means the term and value cannot be stored in an array, dictionary or similar and accessed after the callback has returned.
            Use the functionality Dup on expressions to create a duplicate copy that extends the lifetime.
            </summary>                
        </member>
        <member name="T:Microsoft.Z3.UserPropagator.EqEh">
            <summary>
            Delegate type for equality or disequality callback
            </summary>                
        </member>
        <member name="T:Microsoft.Z3.UserPropagator.CreatedEh">
            <summary>
            Delegate type for when a new term using a registered function symbol is created internally
            </summary>                
        </member>
        <member name="T:Microsoft.Z3.UserPropagator.DecideEh">
            <summary>
            Delegate type for callback into solver's branching. The values can be overridden by calling <see cref="M:Microsoft.Z3.UserPropagator.NextSplit(Microsoft.Z3.Expr,System.UInt32,System.Int32)" />.
            </summary>
            <param name="term">A bit-vector or Boolean used for branching</param>
            <param name="idx">If the term is a bit-vector, then an index into the bit-vector being branched on</param>
            <param name="phase">The tentative truth-value</param>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.#ctor(Microsoft.Z3.Solver)">
            <summary>
            Propagator constructor from a solver class.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.#ctor(Microsoft.Z3.Context)">
            <summary>
            Propagator constructor from a context. It is used from inside of Fresh.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Finalize">
            <summary>
            Release private memory.
            </summary>            
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Dispose">
            <summary>
            Must be called. The object will not be garbage collected automatically even if the context is disposed
            </summary>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Push">
            <summary>
            Virtual method for push. It must be overwritten by inherited class.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Pop(System.UInt32)">
            <summary>
            Virtual method for pop. It must be overwritten by inherited class.
            </summary>        
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Fresh(Microsoft.Z3.Context)">
            <summary>
            Virtual method for fresh. It can be overwritten by inherited class.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Conflict(Microsoft.Z3.Expr[])">
            <summary>
            Declare combination of assigned expressions a conflict
            </summary>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Conflict(System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr})">
            <summary>
            Declare combination of assigned expressions a conflict
            </summary>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Propagate(System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr},Microsoft.Z3.Expr)">
            <summary>
            Propagate consequence
            <returns>
            <see langword="true" /> if the propagated expression is new for the solver;
            <see langword="false" /> if the propagation was ignored
            </returns>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Propagate(System.Collections.Generic.IEnumerable{Microsoft.Z3.Expr},Microsoft.Z3.EqualityPairs,Microsoft.Z3.Expr)">
            <summary>
            Propagate consequence
            <returns>
            <see langword="true" /> if the propagated expression is new for the solver;
            <see langword="false" /> if the propagation was ignored
            </returns>
            </summary>
        </member>
        <member name="P:Microsoft.Z3.UserPropagator.Fixed">
            <summary>
            Set fixed callback
            </summary>
        </member>
        <member name="P:Microsoft.Z3.UserPropagator.Final">
            <summary>
            Set final callback
            </summary>
        </member>
        <member name="P:Microsoft.Z3.UserPropagator.Eq">
            <summary>
            Set equality event callback
            </summary>
        </member>
        <member name="P:Microsoft.Z3.UserPropagator.Diseq">
            <summary>
            Set disequality event callback
            </summary>
        </member>
        <member name="P:Microsoft.Z3.UserPropagator.Created">
            <summary>
            Set created callback
            </summary>
        </member>
        <member name="P:Microsoft.Z3.UserPropagator.Decide">
            <summary>
            Set decision callback
            </summary>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.NextSplit(Microsoft.Z3.Expr,System.UInt32,System.Int32)">
            <summary>
            Set the next decision
            <param name="e">A bit-vector or Boolean used for branching. Use <see langword="null" /> to clear</param>
            <param name="idx">If the term is a bit-vector, then an index into the bit-vector being branched on</param>
            <param name="phase">The tentative truth-value (-1/false, 1/true, 0/let Z3 decide)</param>
            </summary>
            <returns>
            <see langword="true" /> in case the value was successfully set;
            <see langword="false" /> if the next split could not be set
            </returns>
        </member>
        <member name="M:Microsoft.Z3.UserPropagator.Register(Microsoft.Z3.Expr)">
            <summary>
            Track assignments to a term
            </summary>
        </member>
        <member name="T:Microsoft.Z3.EqualityPairs">
            <summary>
            A list of equalities used as justifications for propagation
            </summary>
        </member>
        <member name="P:Microsoft.Z3.EqualityPairs.LHS">
            <summary>
            The left hand sides of the equalities
            </summary>
        </member>
        <member name="P:Microsoft.Z3.EqualityPairs.RHS">
            <summary>
            The right hand sides of the equalities
            </summary>
        </member>
        <member name="P:Microsoft.Z3.EqualityPairs.Count">
            <summary>
            The number of equalities
            </summary>
        </member>
        <member name="M:Microsoft.Z3.EqualityPairs.Add(Microsoft.Z3.Expr,Microsoft.Z3.Expr)">
            <summary>
            Adds an equality to the list. The sorts of the arguments have to be the same.
            <param name="lhs">The left hand side of the equality</param>
            <param name="rhs">The right hand side of the equality</param>
            </summary>
        </member>
        <member name="M:Microsoft.Z3.EqualityPairs.Equals(System.Object)">
            <summary>
            Checks if two equality lists are equal.
            The function does not take symmetries, shuffling, or duplicates into account.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.EqualityPairs.GetHashCode">
            <summary>
            Gets a hash code for the list of equalities
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Version">
            <summary>
            Version information.
            </summary>
            <remarks>Note that this class is static.</remarks>
        </member>
        <member name="P:Microsoft.Z3.Version.Major">
            <summary>
            The major version
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Version.Minor">
            <summary>
            The minor version
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Version.Build">
            <summary>
            The build version
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Version.Revision">
            <summary>
            The revision
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Version.FullVersion">
            <summary>
            A full version string
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Version.ToString">
            <summary>
            A string representation of the version information.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Z3Exception">
            <summary>
            The exception base class for error reporting from Z3
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Z3Exception.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Z3Exception.#ctor(System.String)">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Z3Exception.#ctor(System.String,System.Exception)">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Z3Object">
            <summary>
            Internal base class for interfacing with native Z3 objects.
            Should not be used externally.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Z3Object.Finalize">
            <summary>
            Finalizer.
            </summary>
        </member>
        <member name="M:Microsoft.Z3.Z3Object.Dispose">
            <summary>
            Disposes of the underlying native Z3 object.
            </summary>
        </member>
        <member name="P:Microsoft.Z3.Z3Object.Context">
            <summary>
            Access Context object 
            </summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_lbool">
            <summary>Z3_lbool</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_symbol_kind">
            <summary>Z3_symbol_kind</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_parameter_kind">
            <summary>Z3_parameter_kind</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_sort_kind">
            <summary>Z3_sort_kind</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_ast_kind">
            <summary>Z3_ast_kind</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_decl_kind">
            <summary>Z3_decl_kind</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_param_kind">
            <summary>Z3_param_kind</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_ast_print_mode">
            <summary>Z3_ast_print_mode</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_error_code">
            <summary>Z3_error_code</summary>
        </member>
        <member name="T:Microsoft.Z3.Z3_goal_prec">
            <summary>Z3_goal_prec</summary>
        </member>
    </members>
</doc>
