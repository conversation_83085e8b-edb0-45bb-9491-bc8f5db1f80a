#!/usr/bin/env python3
"""
Estimate the cost of OpenAI text embeddings for the full PropertyGPT dataset.
"""

import os
import csv
from pathlib import Path
import tiktoken

def count_tokens(text, model="text-embedding-3-large"):
    """Count tokens in text using tiktoken."""
    try:
        # Use cl100k_base encoding (used by text-embedding-3-* models)
        encoding = tiktoken.get_encoding("cl100k_base")
        return len(encoding.encode(text))
    except:
        # Fallback: rough estimate of 4 chars per token
        return len(text) // 4

def analyze_csv_dataset(csv_path):
    """Analyze the CSV dataset to estimate embedding costs."""
    
    print(f"🔍 Analyzing CSV dataset: {csv_path}")
    
    total_rows = 0
    total_chars = 0
    total_tokens = 0
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for i, row in enumerate(reader):
                total_rows += 1
                
                # Combine relevant text fields for embedding
                text_content = ""
                
                # Key fields that would be useful for embeddings
                fields_to_embed = ['RuleContent', 'RuleContentNL', 'FunctionBodies', 'Name', 'Type']
                
                for field in fields_to_embed:
                    if field in row and row[field]:
                        text_content += f"{field}: {row[field]}\n"
                
                char_count = len(text_content)
                token_count = count_tokens(text_content)
                
                total_chars += char_count
                total_tokens += token_count
                
                # Progress indicator
                if total_rows % 5000 == 0:
                    print(f"  📊 Processed {total_rows:,} rows... ({total_tokens:,} tokens so far)")
    
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return None
    
    return {
        'total_rows': total_rows,
        'total_chars': total_chars,
        'total_tokens': total_tokens,
        'avg_tokens_per_row': total_tokens / total_rows if total_rows > 0 else 0
    }

def analyze_spec_files(spec_dir):
    """Analyze specification files in the directory."""
    
    print(f"🔍 Analyzing spec files in: {spec_dir}")
    
    total_files = 0
    total_chars = 0
    total_tokens = 0
    
    # File extensions to include
    spec_extensions = ['.spec', '.sol', '.md', '.txt', '.cvl']
    
    for file_path in Path(spec_dir).rglob('*'):
        if file_path.is_file() and file_path.suffix.lower() in spec_extensions:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                # Skip very small files (likely not useful)
                if len(content) < 50:
                    continue
                    
                char_count = len(content)
                token_count = count_tokens(content)
                
                total_files += 1
                total_chars += char_count
                total_tokens += token_count
                
                if total_files % 50 == 0:
                    print(f"  📁 Processed {total_files} files... ({total_tokens:,} tokens so far)")
                    
            except Exception as e:
                print(f"  ⚠️ Error reading {file_path}: {e}")
    
    return {
        'total_files': total_files,
        'total_chars': total_chars,
        'total_tokens': total_tokens,
        'avg_tokens_per_file': total_tokens / total_files if total_files > 0 else 0
    }

def calculate_openai_costs(total_tokens):
    """Calculate OpenAI embedding costs."""
    
    # OpenAI Embedding Pricing (as of 2025)
    pricing = {
        'text-embedding-3-large': {
            'price_per_1m': 0.13,
            'dimensions': 3072,
            'description': 'Highest quality, most expensive'
        },
        'text-embedding-3-small': {
            'price_per_1m': 0.02,
            'dimensions': 1536,
            'description': 'Good quality, very cost-effective'
        },
        'text-embedding-ada-002': {
            'price_per_1m': 0.10,
            'dimensions': 1536,
            'description': 'Legacy model, decent quality'
        }
    }
    
    costs = {}
    for model, info in pricing.items():
        cost = (total_tokens / 1_000_000) * info['price_per_1m']
        costs[model] = {
            'cost': cost,
            'dimensions': info['dimensions'],
            'description': info['description']
        }
    
    return costs

def main():
    print("💰 OpenAI Embedding Cost Estimation for PropertyGPT Dataset")
    print("=" * 70)
    
    total_tokens = 0
    
    # Analyze CSV dataset
    csv_path = "certora_projects/combined_output_train_all.csv"
    if Path(csv_path).exists():
        print("\n📊 CSV Dataset Analysis")
        print("-" * 40)
        csv_stats = analyze_csv_dataset(csv_path)
        
        if csv_stats:
            print(f"✅ Total rows: {csv_stats['total_rows']:,}")
            print(f"✅ Total characters: {csv_stats['total_chars']:,}")
            print(f"✅ Total tokens: {csv_stats['total_tokens']:,}")
            print(f"✅ Average tokens per row: {csv_stats['avg_tokens_per_row']:.1f}")
            total_tokens += csv_stats['total_tokens']
        else:
            print("❌ Failed to analyze CSV dataset")
    else:
        print(f"❌ CSV file not found: {csv_path}")
    
    # Analyze spec files
    spec_dir = "datasets/specs"
    if Path(spec_dir).exists():
        print(f"\n📁 Spec Files Analysis")
        print("-" * 40)
        spec_stats = analyze_spec_files(spec_dir)
        
        print(f"✅ Total files: {spec_stats['total_files']:,}")
        print(f"✅ Total characters: {spec_stats['total_chars']:,}")
        print(f"✅ Total tokens: {spec_stats['total_tokens']:,}")
        print(f"✅ Average tokens per file: {spec_stats['avg_tokens_per_file']:.1f}")
        total_tokens += spec_stats['total_tokens']
    else:
        print(f"❌ Spec directory not found: {spec_dir}")
    
    # Calculate costs
    if total_tokens > 0:
        print(f"\n💰 OpenAI Embedding Cost Analysis")
        print("-" * 40)
        print(f"📊 Total tokens to embed: {total_tokens:,}")
        print(f"📊 Equivalent to ~{total_tokens/1000:.1f}K tokens or ~{total_tokens/1_000_000:.2f}M tokens")
        
        costs = calculate_openai_costs(total_tokens)
        
        print(f"\n💸 Cost breakdown by OpenAI model:")
        print("-" * 40)
        
        for model, info in costs.items():
            print(f"🤖 {model}")
            print(f"   💰 Cost: ${info['cost']:.2f}")
            print(f"   📐 Dimensions: {info['dimensions']}")
            print(f"   📝 {info['description']}")
            print()
        
        # Find cheapest and most expensive
        cheapest = min(costs.items(), key=lambda x: x[1]['cost'])
        most_expensive = max(costs.items(), key=lambda x: x[1]['cost'])
        
        print(f"🎯 Recommendations:")
        print(f"   💚 Cheapest: {cheapest[0]} - ${cheapest[1]['cost']:.2f}")
        print(f"   💎 Highest Quality: {most_expensive[0]} - ${most_expensive[1]['cost']:.2f}")
        print(f"   💡 Recommended: text-embedding-3-small (best value for money)")
        
        print(f"\n⚠️  Important Notes:")
        print(f"   • These are estimates based on current OpenAI pricing")
        print(f"   • Actual costs may vary slightly due to tokenization differences")
        print(f"   • Consider chunking very large documents to optimize performance")
        print(f"   • Batch processing (up to 2048 inputs per request) can improve efficiency")
        print(f"   • You'll need an OpenAI API key with sufficient credits")
        
        # Storage estimates
        print(f"\n💾 Storage Requirements (FAISS index):")
        print(f"   • text-embedding-3-small: ~{(total_tokens * 1536 * 4) / (1024**2):.1f} MB")
        print(f"   • text-embedding-3-large: ~{(total_tokens * 3072 * 4) / (1024**2):.1f} MB")
        print(f"   (Assuming 4 bytes per float32 dimension)")
        
    else:
        print(f"\n❌ No data found to analyze")
        print(f"Make sure the dataset files exist:")
        print(f"  • {csv_path}")
        print(f"  • {spec_dir}")

if __name__ == "__main__":
    main()
