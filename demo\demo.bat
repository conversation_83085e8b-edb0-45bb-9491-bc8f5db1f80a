@echo off
REM PropertyGPT Demo Script for Windows
REM This script demonstrates PropertyGPT's ability to find bugs in smart contracts

echo 🚀 PropertyGPT Demo - Smart Contract Bug Discovery
echo ==================================================
echo.

REM Check if we're in the right directory
if not exist "demo\Bank.sol" (
    echo ❌ Error: Please run this script from the project root directory
    exit /b 1
)

REM Check if PropertyGPT is installed
property-gpt --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: property-gpt command not found. Please install PropertyGPT first:
    echo    pip install -e .
    exit /b 1
)

REM Check for required environment variables
if "%OPENAI_API_KEY%"=="" (
    echo ❌ Error: OPENAI_API_KEY environment variable not set
    echo    Please set your OpenAI API key:
    echo    set OPENAI_API_KEY=your-api-key-here
    exit /b 1
)

echo ✅ Environment checks passed
echo.

REM Step 1: Build knowledge base (if not already exists)
if not exist "kb" (
    echo 📚 Step 1: Building knowledge base from Certora specifications...
    echo This may take a few minutes as we process and embed the specification files...
    
    property-gpt build-kb datasets\specs --output kb
    
    if errorlevel 1 (
        echo ❌ Failed to build knowledge base
        exit /b 1
    )
    
    echo ✅ Knowledge base built successfully!
) else (
    echo 📚 Knowledge base already exists, skipping build step
)

echo.

REM Step 2: Analyze the vulnerable Bank contract
echo 🔍 Step 2: Analyzing Bank.sol for security vulnerabilities...
echo PropertyGPT will:
echo   1. Parse the contract with Slither
echo   2. Retrieve similar properties from the knowledge base
echo   3. Generate invariants using GPT-4o
echo   4. Compile and fix the generated invariants
echo   5. Prove/disprove invariants with Z3
echo   6. Generate a comprehensive report
echo.

REM Run the analysis
property-gpt analyse demo\Bank.sol --output demo\bank_analysis.md --verbose

REM Capture the exit code
set ANALYSIS_EXIT_CODE=%errorlevel%

echo.
echo 📊 Analysis Results:
echo ===================

if %ANALYSIS_EXIT_CODE%==1 (
    echo 🚨 PropertyGPT found potential security vulnerabilities!
    echo.
    echo 📄 Detailed report saved to: demo\bank_analysis.md
    echo 📄 JSON summary saved to: demo\bank_analysis.json
    echo.
    echo 🔍 Key findings should include:
    echo   • Unchecked integer underflow in withdraw^(^) function
    echo   • Missing balance validation
    echo   • Potential state inconsistency in emergencyWithdraw^(^)
    echo.
    echo ✅ Demo completed successfully - bugs detected as expected!
    
) else if %ANALYSIS_EXIT_CODE%==0 (
    echo ⚠️  No vulnerabilities detected ^(unexpected for this demo^)
    echo 📄 Report saved to: demo\bank_analysis.md
    echo.
    echo This might indicate:
    echo   • The invariant generation didn't cover the vulnerable patterns
    echo   • The SMT solver couldn't find counterexamples
    echo   • The analysis parameters need adjustment
    
) else (
    echo ❌ Analysis failed with exit code %ANALYSIS_EXIT_CODE%
    echo Check the logs above for error details
    exit /b 1
)

echo.
echo 🎯 Demo Summary:
echo ===============
echo • Contract analyzed: demo\Bank.sol
echo • Knowledge base: Check kb directory for specification chunks
echo • Analysis time: Check the report for timing details
echo • Report location: demo\bank_analysis.md
echo.
echo 📖 Next steps:
echo   1. Review the generated report: demo\bank_analysis.md
echo   2. Examine the JSON summary: demo\bank_analysis.json
echo   3. Try analyzing your own contracts with: property-gpt analyse ^<contract.sol^>
echo   4. Build custom knowledge bases with: property-gpt build-kb ^<spec-dir^>
echo.
echo 🔗 For more information, see the README.md file
echo.
echo ✨ PropertyGPT demo completed!
