{"contract_path": "demo\\Bank.sol", "analysis_timestamp": "2025-07-10T15:59:23.386876", "analysis_time_seconds": 792.************, "total_invariants": 3, "results": {"violated": 0, "verified": 3, "timeout": 0, "unknown": 0}, "invariants": [{"id": "setOwner_post_0", "description": "Postcondition 1 for setOwner", "function_name": "<PERSON><PERSON><PERSON><PERSON>", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.234, "counter_example": null}, {"id": "getContractBalance_post_3", "description": "Postcondition 4 for getContractBalance", "function_name": "getContractBalance", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.228, "counter_example": null}, {"id": "batchDeposit_cross_1", "description": "Cross-invariant 2 for batchDeposit", "function_name": "batchDeposit", "property_type": "cross_invariant", "proof_status": "verified", "confidence_score": 0.228, "counter_example": null}], "metadata": {"model": "deepseek-chat", "api_provider": "deepseek", "solver": "z3", "k_similar": 3, "temperature": 0.2, "timeout": 30, "kb_available": true}}