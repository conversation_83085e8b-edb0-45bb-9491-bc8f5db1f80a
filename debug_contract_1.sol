// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title Bank
 * @dev A simple bank contract with an intentional underflow vulnerability
 * This contract demonstrates a classic integer underflow bug that PropertyGPT should detect
 */
contract Bank {
    mapping(address => uint256) public balances;
    uint256 public totalSupply;
    address public owner;
    
    event Deposit(address indexed user, uint256 amount);
    event Withdrawal(address indexed user, uint256 amount);
    event Transfer(address indexed from, address indexed to, uint256 amount);
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    constructor() {
        owner = msg.sender;
        totalSupply = 0;
    }
    
    /**
     * @dev Deposit funds into the bank
     * @param amount Amount to deposit
     */
    function deposit(uint256 amount) external payable {
        require(amount > 0, "Amount must be positive");
        require(msg.value == amount, "Sent value must match amount");
        
        balances[msg.sender] += amount;
        totalSupply += amount;
        
        emit Deposit(msg.sender, amount);
    }
    
    /**
     * @dev Withdraw funds from the bank
     * @param amount Amount to withdraw
     * 
     * BUG: This function has an unchecked underflow vulnerability!
     * If amount > balances[msg.sender], the subtraction will underflow
     * and wrap around to a very large number.
     */
    function withdraw(uint256 amount) external {
        require(amount > 0, "Amount must be positive");
        
        // BUG: Missing balance check allows underflow
        // Should check: require(balances[msg.sender] >= amount, "Insufficient balance");
        
        balances[msg.sender] -= amount;  // VULNERABLE: Can underflow!
        totalSupply -= amount;           // VULNERABLE: Can underflow!
        
        // Send the funds
        payable(msg.sender).transfer(amount);
        
        emit Withdrawal(msg.sender, amount);
    }
    
    /**
     * @dev Transfer funds between accounts
     * @param to Recipient address
     * @param amount Amount to transfer
     */
    function transfer(address to, uint256 amount) external {
        require(to != address(0), "Cannot transfer to zero address");
        require(amount > 0, "Amount must be positive");
        require(balances[msg.sender] >= amount, "Insufficient balance");
        
        balances[msg.sender] -= amount;
        balances[to] += amount;
        
        emit Transfer(msg.sender, to, amount);
    }
    
    /**
     * @dev Get balance of an account
     * @param account Account to check
     * @return Balance of the account
     */
    function getBalance(address account) external view returns (uint256) {
        return balances[account];
    }
    
    /**
     * @dev Emergency withdrawal by owner (another potential vulnerability)
     * @param amount Amount to withdraw
     */
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        require(amount <= address(this).balance, "Insufficient contract balance");
        
        // BUG: This doesn't update balances or totalSupply!
        // This breaks the invariant that totalSupply == sum of all balances
        payable(owner).transfer(amount);
    }
    
    /**
     * @dev Get contract's ETH balance
     * @return Contract balance
     */
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev Batch deposit for multiple users (gas optimization)
     * @param users Array of user addresses
     * @param amounts Array of amounts to deposit for each user
     */
    function batchDeposit(address[] calldata users, uint256[] calldata amounts) external payable onlyOwner {
        require(users.length == amounts.length, "Arrays length mismatch");
        
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            totalAmount += amounts[i];
        }
        require(msg.value == totalAmount, "Sent value must match total amount");
        
        for (uint256 i = 0; i < users.length; i++) {
            require(amounts[i] > 0, "Amount must be positive");
            balances[users[i]] += amounts[i];
            emit Deposit(users[i], amounts[i]);
        }
        
        totalSupply += totalAmount;
    }
    
    /**
     * @dev Set a new owner (potential access control issue)
     * @param newOwner New owner address
     */
    function setOwner(address newOwner) external onlyOwner {
        require(newOwner != address(0), "New owner cannot be zero address");
        owner = newOwner;
    }
    
    /**
     * @dev Fallback function to receive ETH
     */
    receive() external payable {
        // Allow contract to receive ETH directly
        // This could be problematic as it doesn't update balances
    }
}


contract TestInvariant {
    Bank bank;

    constructor(address _bank) {
        bank = Bank(_bank);
    }

    function testInvariant() public view {
        require(msg.value > 0, "Deposit amount must be positive");
    }
}
