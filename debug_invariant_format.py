#!/usr/bin/env python3
"""
Debug the exact invariant format that PropertyGPT generates.
"""

import os
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def test_exact_format():
    """Test the exact format PropertyGPT generates."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a simple test function
        test_function = ContractFunction(
            name="withdraw",
            signature="withdraw(uint256)",
            visibility="external",
            state_mutability="nonpayable",
            parameters=[{"name": "amount", "type": "uint256"}],
            returns=[]
        )
        
        print("🧠 Generating one invariant...")
        
        # Generate just one invariant
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": generator._create_generation_prompt(test_function, [])}],
            temperature=0.2,
            max_tokens=500
        )
        
        generated_text = response.choices[0].message.content
        print(f"📝 Generated text: {generated_text}")
        
        # Parse invariants
        invariants = generator._parse_generated_invariants(generated_text, test_function)
        
        if invariants:
            print(f"\n🔍 Testing compilation of {len(invariants)} invariants:")
            
            for i, inv in enumerate(invariants[:3]):  # Test first 3
                print(f"\n--- Invariant {i+1} ---")
                print(f"Type: {inv.property_type}")
                print(f"Code: {inv.code}")
                
                # Test compilation exactly like PropertyGPT does
                from pathlib import Path
                compiled_inv = generator._compile_and_fix_invariant(inv, Path("demo/Bank.sol"), max_retries=1)
                success = compiled_inv is not None and compiled_inv.compilation_status == "success"
                print(f"Compilation: {'✅ SUCCESS' if success else '❌ FAILED'}")

                if compiled_inv:
                    print(f"Status: {compiled_inv.compilation_status}")
                    if compiled_inv.compilation_status == "failed":
                        print(f"Error: {compiled_inv.compilation_error}")

                if not success:
                    # Debug the exact contract that's being compiled
                    contract_code = generator._create_test_contract(inv, "demo/Bank.sol")
                    print(f"Generated contract preview:")
                    print(contract_code[:500] + "..." if len(contract_code) > 500 else contract_code)
        else:
            print("❌ No invariants parsed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_exact_format()
