#!/usr/bin/env python3
"""
Test simple invariant compilation.
"""

from property_gpt.utils import create_temp_file, run_command

def test_simple_invariant():
    """Test compiling a simple invariant."""
    
    # Create a simple contract with an invariant
    contract_code = """
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Bank {
    mapping(address => uint256) public balances;
    uint256 public totalSupply;
    
    function deposit() external payable {
        balances[msg.sender] += msg.value;
        totalSupply += msg.value;
    }
    
    function withdraw(uint256 amount) external {
        require(balances[msg.sender] >= amount, "Insufficient balance");
        balances[msg.sender] -= amount;
        totalSupply -= amount;
    }
}

// Simple invariant test
contract InvariantTest {
    function test_balance_invariant() public pure {
        // This should always be true
        require(true, "Simple test");
    }
}
"""
    
    print("🧪 Testing simple invariant compilation...")
    
    temp_file = create_temp_file(contract_code)
    
    try:
        # Test compilation
        cmd = ["solc", str(temp_file)]
        returncode, stdout, stderr = run_command(cmd)
        
        print(f"Return code: {returncode}")
        if returncode == 0:
            print("✅ Simple invariant compiles successfully")
            print(f"Output: {stdout[:200]}...")
        else:
            print(f"❌ Compilation failed: {stderr}")
            
        return returncode == 0
        
    finally:
        temp_file.unlink(missing_ok=True)

def test_realistic_invariant():
    """Test compiling a more realistic invariant."""
    
    # Create a contract with a realistic invariant
    contract_code = """
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Bank {
    mapping(address => uint256) public balances;
    uint256 public totalSupply;
    
    function withdraw(uint256 amount) external {
        require(balances[msg.sender] >= amount, "Insufficient balance");
        balances[msg.sender] -= amount;
        totalSupply -= amount;
    }
}

// Realistic invariant test
contract InvariantTest {
    Bank bank;
    
    constructor(address _bank) {
        bank = Bank(_bank);
    }
    
    function test_withdraw_precondition(uint256 amount) public view {
        // Precondition: amount must be <= sender balance
        require(amount <= bank.balances(msg.sender), "Withdrawal amount exceeds balance");
    }
}
"""
    
    print("\n🧪 Testing realistic invariant compilation...")
    
    temp_file = create_temp_file(contract_code)
    
    try:
        # Test compilation
        cmd = ["solc", str(temp_file)]
        returncode, stdout, stderr = run_command(cmd)
        
        print(f"Return code: {returncode}")
        if returncode == 0:
            print("✅ Realistic invariant compiles successfully")
        else:
            print(f"❌ Compilation failed: {stderr}")
            
        return returncode == 0
        
    finally:
        temp_file.unlink(missing_ok=True)

if __name__ == "__main__":
    print("🚀 Testing Invariant Compilation")
    print("=" * 40)
    
    simple_ok = test_simple_invariant()
    realistic_ok = test_realistic_invariant()
    
    print("\n" + "=" * 40)
    print("🎯 Test Summary:")
    print(f"  Simple invariant: {'✅ PASS' if simple_ok else '❌ FAIL'}")
    print(f"  Realistic invariant: {'✅ PASS' if realistic_ok else '❌ FAIL'}")
    
    if simple_ok and realistic_ok:
        print("\n🎉 Invariant compilation is working!")
    else:
        print("\n⚠️ Invariant compilation issues detected.")
