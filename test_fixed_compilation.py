#!/usr/bin/env python3
"""
Test the fixed compilation with a manually created invariant.
"""

from pathlib import Path
from property_gpt.utils import InvariantProperty, run_command
from property_gpt.gen_props import InvariantGenerator

def test_fixed_compilation():
    """Test compilation with a properly formatted invariant."""
    
    print("🧪 Testing fixed compilation...")
    
    # Create a generator (no client needed for this test)
    generator = InvariantGenerator(None, None, "test", 0.2)
    
    # Create a test invariant that should work
    test_invariant = InvariantProperty(
        id="test_withdraw_pre_0",
        property_type="precondition",
        function_name="withdraw",
        code="require(bank.owner() == msg.sender, \"Only owner can withdraw\");",
        description="Only owner can withdraw funds",
        confidence_score=0.9
    )
    
    print(f"🔍 Testing invariant: {test_invariant.code}")
    
    # Test contract creation
    contract_code = generator._create_test_contract(test_invariant, Path("demo/Bank.sol"))
    
    # Save for inspection
    with open("test_fixed_contract.sol", "w") as f:
        f.write(contract_code)
    
    print("✅ Contract saved to test_fixed_contract.sol")
    
    # Test compilation
    cmd = ["solc", "test_fixed_contract.sol"]
    returncode, stdout, stderr = run_command(cmd)
    
    print(f"Return code: {returncode}")
    if returncode == 0:
        print("✅ Compilation succeeded!")
        return True
    else:
        print(f"❌ Compilation failed: {stderr}")
        return False

def test_simple_invariant():
    """Test with a very simple invariant that should always work."""
    
    print("\n🧪 Testing simple invariant...")
    
    generator = InvariantGenerator(None, None, "test", 0.2)
    
    # Create a very simple invariant
    simple_invariant = InvariantProperty(
        id="test_simple",
        property_type="precondition",
        function_name="withdraw",
        code="require(true, \"This should always pass\");",
        description="Simple test invariant",
        confidence_score=0.9
    )
    
    print(f"🔍 Testing simple invariant: {simple_invariant.code}")
    
    # Test contract creation
    contract_code = generator._create_test_contract(simple_invariant, Path("demo/Bank.sol"))
    
    # Save for inspection
    with open("test_simple_contract.sol", "w") as f:
        f.write(contract_code)
    
    print("✅ Contract saved to test_simple_contract.sol")
    
    # Test compilation
    cmd = ["solc", "test_simple_contract.sol"]
    returncode, stdout, stderr = run_command(cmd)
    
    print(f"Return code: {returncode}")
    if returncode == 0:
        print("✅ Simple compilation succeeded!")
        return True
    else:
        print(f"❌ Simple compilation failed: {stderr}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Fixed Compilation")
    print("=" * 40)
    
    simple_ok = test_simple_invariant()
    fixed_ok = test_fixed_compilation()
    
    print("\n" + "=" * 40)
    print("🎯 Test Summary:")
    print(f"  Simple invariant: {'✅ PASS' if simple_ok else '❌ FAIL'}")
    print(f"  Fixed invariant: {'✅ PASS' if fixed_ok else '❌ FAIL'}")
    
    if simple_ok and fixed_ok:
        print("\n🎉 Fixed compilation is working!")
    else:
        print("\n⚠️ Compilation issues still exist.")
