"""
Unit tests for PropertyGPT.

This module contains comprehensive tests for all PropertyGPT components,
using mocked OpenAI client to avoid API calls during testing.
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import pytest
import numpy as np
import faiss

from property_gpt.utils import (
    PropertyGPTError,
    InvariantProperty,
    ContractFunction,
    SimilarProperty,
    extract_functions_from_spec,
    calculate_edit_distance,
    calculate_keyword_coverage,
    calculate_complexity_score,
)
from property_gpt.kb import KnowledgeBaseBuilder, KnowledgeBaseSearcher
from property_gpt.gen_props import InvariantGenerator
from property_gpt.prover import InvariantProver
from property_gpt.report import ReportGenerator


class TestUtils:
    """Test utility functions."""
    
    def test_extract_functions_from_spec(self):
        """Test extraction of functions from specification content."""
        spec_content = """
        methods {
            balanceOf(address) returns uint256 envfree
            transfer(address, uint256) returns bool
        }
        
        rule integrityTransfer(address from, address to, uint256 amount) {
            env e;
            require from != to;
            uint256 balanceFromBefore = balanceOf(from);
            uint256 balanceToBefore = balanceOf(to);
            
            transfer(e, to, amount);
            
            uint256 balanceFromAfter = balanceOf(from);
            uint256 balanceToAfter = balanceOf(to);
            
            assert balanceFromAfter == balanceFromBefore - amount;
            assert balanceToAfter == balanceToBefore + amount;
        }
        """
        
        functions = extract_functions_from_spec(spec_content)
        
        assert len(functions) >= 2
        assert any("balanceOf" in func for func in functions)
        assert any("transfer" in func for func in functions)
        assert any("rule integrityTransfer" in func for func in functions)
    
    def test_calculate_edit_distance(self):
        """Test edit distance calculation."""
        assert calculate_edit_distance("", "") == 0
        assert calculate_edit_distance("abc", "abc") == 0
        assert calculate_edit_distance("abc", "ab") == 1
        assert calculate_edit_distance("abc", "def") == 3
    
    def test_calculate_keyword_coverage(self):
        """Test keyword coverage calculation."""
        text = "require(balance >= amount, 'insufficient balance')"
        keywords = ["balance", "amount", "require"]
        
        coverage = calculate_keyword_coverage(text, keywords)
        assert coverage == 1.0  # All keywords present
        
        keywords_partial = ["balance", "amount", "missing"]
        coverage_partial = calculate_keyword_coverage(text, keywords_partial)
        assert coverage_partial == 2.0 / 3.0  # 2 out of 3 keywords
    
    def test_calculate_complexity_score(self):
        """Test complexity score calculation."""
        simple_code = "assert(x > 0);"
        complex_code = """
        require(balance >= amount, "insufficient balance");
        if (amount > 0) {
            for (uint i = 0; i < 10; i++) {
                balance -= amount;
            }
        }
        assert(balance >= 0);
        """
        
        simple_score = calculate_complexity_score(simple_code)
        complex_score = calculate_complexity_score(complex_code)
        
        assert complex_score > simple_score
        assert simple_score > 0
        assert complex_score <= 10.0  # Capped at 10


class TestKnowledgeBase:
    """Test knowledge base functionality."""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Create a mock OpenAI client."""
        client = Mock()
        
        # Mock embedding response
        embedding_response = Mock()
        embedding_response.data = [
            Mock(embedding=[0.1] * 3072),
            Mock(embedding=[0.2] * 3072),
        ]
        client.embeddings.create.return_value = embedding_response
        
        return client
    
    @pytest.fixture
    def temp_spec_dir(self):
        """Create temporary directory with spec files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            spec_dir = Path(temp_dir) / "specs"
            spec_dir.mkdir()
            
            # Create sample spec file
            spec_file = spec_dir / "test.spec"
            spec_file.write_text("""
            methods {
                balanceOf(address) returns uint256 envfree
            }
            
            rule balanceNonNegative(address user) {
                assert balanceOf(user) >= 0;
            }
            """)
            
            yield spec_dir
    
    def test_knowledge_base_builder(self, mock_openai_client, temp_spec_dir):
        """Test knowledge base building."""
        with tempfile.TemporaryDirectory() as output_dir:
            builder = KnowledgeBaseBuilder(mock_openai_client)
            
            files_processed, chunks_created = builder.build_knowledge_base(
                temp_spec_dir, Path(output_dir)
            )
            
            assert files_processed == 1
            assert chunks_created > 0
            
            # Check output files exist
            assert (Path(output_dir) / "kb.index").exists()
            assert (Path(output_dir) / "kb.jsonl").exists()
            assert (Path(output_dir) / "kb_config.json").exists()
    
    def test_knowledge_base_searcher(self, mock_openai_client):
        """Test knowledge base searching."""
        with tempfile.TemporaryDirectory() as kb_dir:
            kb_path = Path(kb_dir)
            
            # Create mock KB files
            # Create a simple FAISS index
            index = faiss.IndexFlatIP(3072)
            embeddings = np.random.random((2, 3072)).astype(np.float32)
            faiss.normalize_L2(embeddings)
            index.add(embeddings)
            faiss.write_index(index, str(kb_path / "kb.index"))
            
            # Create metadata
            metadata = [
                {"source_file": "test1.spec", "chunk_type": "content"},
                {"source_file": "test2.spec", "chunk_type": "function"},
            ]
            with open(kb_path / "kb.jsonl", 'w') as f:
                for item in metadata:
                    f.write(json.dumps(item) + '\n')
            
            # Create config
            config = {
                "embedding_model": "text-embedding-3-large",
                "embedding_dim": 3072,
                "total_chunks": 2,
                "index_type": "IndexFlatIP"
            }
            with open(kb_path / "kb_config.json", 'w') as f:
                json.dump(config, f)
            
            # Test searcher
            searcher = KnowledgeBaseSearcher(kb_path, mock_openai_client)
            results = searcher.search("test query", k=1)
            
            assert len(results) == 1
            assert "similarity_score" in results[0]


class TestInvariantGenerator:
    """Test invariant generation."""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Create mock OpenAI client for generation."""
        client = Mock()
        
        # Mock chat completion response
        response = Mock()
        response.choices = [Mock()]
        response.choices[0].message.content = """
        PRECONDITIONS:
        1. require(amount > 0, "Amount must be positive");
        2. require(balances[msg.sender] >= amount, "Insufficient balance");
        
        POSTCONDITIONS:
        1. assert(balances[msg.sender] == oldBalance - amount);
        2. assert(totalSupply >= 0);
        
        CROSS_INVARIANTS:
        1. assert(totalSupply == sum_of_all_balances);
        """
        client.chat.completions.create.return_value = response
        
        return client
    
    @pytest.fixture
    def sample_contract(self):
        """Create a sample contract file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sol', delete=False) as f:
            f.write("""
            pragma solidity ^0.8.0;
            
            contract TestContract {
                mapping(address => uint256) public balances;
                
                function transfer(address to, uint256 amount) public {
                    balances[msg.sender] -= amount;
                    balances[to] += amount;
                }
            }
            """)
            return Path(f.name)
    
    def test_invariant_generation(self, mock_openai_client, sample_contract):
        """Test invariant generation process."""
        generator = InvariantGenerator(mock_openai_client)
        
        with patch('property_gpt.gen_props.run_command') as mock_run:
            # Mock Slither output
            mock_run.return_value = (1, "", "Slither failed")  # Force fallback parsing
            
            invariants = generator.generate_invariants(sample_contract, k_similar=3, top_n=3)
            
            # Should generate some invariants even with fallback parsing
            assert len(invariants) >= 0
    
    def test_parse_generated_invariants(self, mock_openai_client):
        """Test parsing of generated invariants."""
        generator = InvariantGenerator(mock_openai_client)
        
        generated_text = """
        PRECONDITIONS:
        1. require(amount > 0, "Amount must be positive");
        
        POSTCONDITIONS:
        1. assert(balance >= 0);
        
        CROSS_INVARIANTS:
        1. assert(totalSupply == sum_balances);
        """
        
        function = ContractFunction(
            name="transfer",
            signature="transfer(address,uint256)",
            visibility="public",
            state_mutability="nonpayable",
            parameters=[],
            returns=[]
        )
        
        invariants = generator._parse_generated_invariants(generated_text, function)
        
        assert len(invariants) == 3
        assert any(inv.property_type == "precondition" for inv in invariants)
        assert any(inv.property_type == "postcondition" for inv in invariants)
        assert any(inv.property_type == "cross_invariant" for inv in invariants)


class TestProver:
    """Test invariant proving."""
    
    @pytest.fixture
    def sample_invariant(self):
        """Create a sample invariant."""
        return InvariantProperty(
            id="test_inv_1",
            description="Test invariant",
            code="assert(x > 0);",
            function_name="testFunction",
            property_type="postcondition"
        )
    
    @pytest.fixture
    def sample_contract(self):
        """Create a sample contract file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sol', delete=False) as f:
            f.write("""
            pragma solidity ^0.8.0;
            contract Test {
                uint256 public x = 1;
                function getX() public view returns (uint256) { return x; }
            }
            """)
            return Path(f.name)
    
    def test_prover_initialization(self):
        """Test prover initialization."""
        prover = InvariantProver("z3", 30)
        assert prover.solver == "z3"
        assert prover.timeout == 30
        
        with pytest.raises(PropertyGPTError):
            InvariantProver("invalid_solver")
    
    def test_extract_condition(self):
        """Test condition extraction from invariant code."""
        prover = InvariantProver()
        
        # Test require statement
        require_code = "require(balance >= amount, 'insufficient balance');"
        condition = prover._extract_condition(require_code)
        assert "balance >= amount" in condition
        
        # Test assert statement
        assert_code = "assert(totalSupply > 0);"
        condition = prover._extract_condition(assert_code)
        assert "totalSupply > 0" in condition
    
    @patch('property_gpt.prover.run_command')
    def test_smt_translation_fallback(self, mock_run, sample_contract, sample_invariant):
        """Test SMT translation fallback."""
        mock_run.return_value = (1, "", "slither-smt failed")
        
        prover = InvariantProver()
        smt_content = prover._translate_to_smt(sample_contract, sample_invariant)
        
        assert smt_content is not None
        assert "set-logic" in smt_content
        assert "check-sat" in smt_content


class TestReportGenerator:
    """Test report generation."""
    
    @pytest.fixture
    def sample_invariants(self):
        """Create sample invariants with different statuses."""
        return [
            InvariantProperty(
                id="violated_1",
                description="Violated invariant",
                code="assert(balance >= 0);",
                function_name="withdraw",
                property_type="postcondition",
                proof_status="violated",
                counter_example={"balance": -100, "amount": 200}
            ),
            InvariantProperty(
                id="verified_1",
                description="Verified invariant",
                code="require(amount > 0);",
                function_name="deposit",
                property_type="precondition",
                proof_status="verified"
            ),
            InvariantProperty(
                id="timeout_1",
                description="Timeout invariant",
                code="assert(complex_condition);",
                function_name="complex",
                property_type="cross_invariant",
                proof_status="timeout"
            )
        ]
    
    def test_report_generation(self, sample_invariants):
        """Test report generation."""
        generator = ReportGenerator()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            contract_path = Path(temp_dir) / "test.sol"
            contract_path.write_text("contract Test {}")
            
            output_path = Path(temp_dir) / "report.md"
            
            success = generator.generate_report(
                contract_path=contract_path,
                invariants=sample_invariants,
                output_path=output_path,
                analysis_time=10.5
            )
            
            assert success
            assert output_path.exists()
            
            # Check report content
            report_content = output_path.read_text()
            assert "PropertyGPT Analysis Report" in report_content
            assert "Violated Properties" in report_content
            assert "Verified Properties" in report_content
            assert "violated_1" in report_content
            assert "verified_1" in report_content
    
    def test_json_summary_generation(self, sample_invariants):
        """Test JSON summary generation."""
        generator = ReportGenerator()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            contract_path = Path(temp_dir) / "test.sol"
            json_path = Path(temp_dir) / "summary.json"
            
            generator._save_json_summary(
                json_path, contract_path, sample_invariants, 10.5, {"test": "metadata"}
            )
            
            assert json_path.exists()
            
            with open(json_path) as f:
                summary = json.load(f)
            
            assert summary["total_invariants"] == 3
            assert summary["results"]["violated"] == 1
            assert summary["results"]["verified"] == 1
            assert summary["results"]["timeout"] == 1
            assert summary["analysis_time_seconds"] == 10.5


class TestIntegration:
    """Integration tests for the full pipeline."""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Create comprehensive mock OpenAI client."""
        client = Mock()
        
        # Mock embedding response
        embedding_response = Mock()
        embedding_response.data = [Mock(embedding=[0.1] * 3072)]
        client.embeddings.create.return_value = embedding_response
        
        # Mock chat completion response
        chat_response = Mock()
        chat_response.choices = [Mock()]
        chat_response.choices[0].message.content = """
        PRECONDITIONS:
        1. require(amount > 0, "Amount must be positive");
        
        POSTCONDITIONS:
        1. assert(balance >= 0);
        
        CROSS_INVARIANTS:
        1. assert(totalSupply >= 0);
        """
        client.chat.completions.create.return_value = chat_response
        
        return client
    
    def test_end_to_end_pipeline(self, mock_openai_client):
        """Test the complete analysis pipeline."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create sample contract
            contract_path = temp_path / "test.sol"
            contract_path.write_text("""
            pragma solidity ^0.8.0;
            contract Test {
                uint256 public balance;
                function withdraw(uint256 amount) public {
                    balance -= amount;  // Potential underflow
                }
            }
            """)
            
            # Test without KB (should still work)
            generator = InvariantGenerator(mock_openai_client)
            prover = InvariantProver("z3", 5)  # Short timeout for tests
            report_gen = ReportGenerator()
            
            # Generate invariants
            with patch('property_gpt.gen_props.run_command') as mock_run:
                mock_run.return_value = (1, "", "Slither failed")  # Force fallback
                invariants = generator.generate_invariants(contract_path, k_similar=1, top_n=1)
            
            # Prove invariants (will likely timeout or fail, but shouldn't crash)
            with patch('property_gpt.prover.run_command') as mock_run:
                mock_run.return_value = (1, "", "slither-smt failed")  # Force fallback
                proved_invariants = prover.prove_invariants(contract_path, invariants)
            
            # Generate report
            report_path = temp_path / "test_report.md"
            success = report_gen.generate_report(
                contract_path, proved_invariants, report_path, analysis_time=5.0
            )
            
            assert success
            assert report_path.exists()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
