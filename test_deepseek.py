#!/usr/bin/env python3
"""
Test script to verify DeepSeek API integration with PropertyGPT.
"""

import os
from property_gpt.utils import create_openai_client

def test_deepseek_connection():
    """Test basic connection to DeepSeek API."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return False
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    try:
        # Create DeepSeek client
        client = create_openai_client(api_key, "https://api.deepseek.com")
        
        # Test chat completion
        print("🤖 Testing DeepSeek chat completion...")
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "Generate a simple Solidity require statement for checking if an amount is positive."}
            ],
            max_tokens=100,
            temperature=0.2
        )
        
        result = response.choices[0].message.content
        print(f"✅ DeepSeek response: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API error: {e}")
        return False

def test_invariant_generation():
    """Test invariant generation with DeepSeek."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return False
    
    try:
        from property_gpt.gen_props import InvariantGenerator
        from property_gpt.utils import ContractFunction
        
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a test function
        test_function = ContractFunction(
            name="withdraw",
            signature="withdraw(uint256)",
            visibility="public",
            state_mutability="nonpayable",
            parameters=[{"name": "amount", "type": "uint256"}],
            returns=[]
        )
        
        print("🧠 Testing invariant generation...")
        
        # Generate prompt
        prompt = generator._create_generation_prompt(test_function, [])
        print(f"📝 Generated prompt (first 200 chars): {prompt[:200]}...")
        
        # Test generation
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=500
        )
        
        generated_text = response.choices[0].message.content
        print(f"✅ Generated invariants: {generated_text[:300]}...")
        
        # Parse invariants
        invariants = generator._parse_generated_invariants(generated_text, test_function)
        print(f"📊 Parsed {len(invariants)} invariants")
        
        for inv in invariants:
            print(f"  - {inv.property_type}: {inv.code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Invariant generation error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing DeepSeek API Integration")
    print("=" * 50)
    
    # Test 1: Basic connection
    print("\n1. Testing basic DeepSeek connection...")
    connection_ok = test_deepseek_connection()
    
    # Test 2: Invariant generation
    if connection_ok:
        print("\n2. Testing invariant generation...")
        generation_ok = test_invariant_generation()
    else:
        generation_ok = False
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print(f"  Connection: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"  Generation: {'✅ PASS' if generation_ok else '❌ FAIL'}")
    
    if connection_ok and generation_ok:
        print("\n🎉 DeepSeek integration is working correctly!")
        print("💡 You can now use: property-gpt analyse contract.sol --api-provider deepseek --model deepseek-chat")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
