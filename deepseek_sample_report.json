{"contract_path": "demo\\Bank.sol", "analysis_timestamp": "2025-07-10T12:22:59.040847", "analysis_time_seconds": 120.0, "total_invariants": 24, "results": {"violated": 0, "verified": 0, "timeout": 0, "unknown": 24}, "invariants": [{"id": "withdraw_pre_0", "description": "Precondition 1 for withdraw", "function_name": "withdraw", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_pre_1", "description": "Precondition 2 for withdraw", "function_name": "withdraw", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_pre_2", "description": "Precondition 3 for withdraw", "function_name": "withdraw", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_pre_3", "description": "Precondition 4 for withdraw", "function_name": "withdraw", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_pre_4", "description": "Precondition 5 for withdraw", "function_name": "withdraw", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_post_0", "description": "Postcondition 1 for withdraw", "function_name": "withdraw", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_post_1", "description": "Postcondition 2 for withdraw", "function_name": "withdraw", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_post_2", "description": "Postcondition 3 for withdraw", "function_name": "withdraw", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_post_3", "description": "Postcondition 4 for withdraw", "function_name": "withdraw", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_post_4", "description": "Postcondition 5 for withdraw", "function_name": "withdraw", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_cross_0", "description": "Cross-invariant 1 for withdraw", "function_name": "withdraw", "property_type": "cross_invariant", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "withdraw_cross_1", "description": "Cross-invariant 2 for withdraw", "function_name": "withdraw", "property_type": "cross_invariant", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_pre_0", "description": "Precondition 1 for deposit", "function_name": "deposit", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_pre_1", "description": "Precondition 2 for deposit", "function_name": "deposit", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_pre_2", "description": "Precondition 3 for deposit", "function_name": "deposit", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_pre_3", "description": "Precondition 4 for deposit", "function_name": "deposit", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_pre_4", "description": "Precondition 5 for deposit", "function_name": "deposit", "property_type": "precondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_post_0", "description": "Postcondition 1 for deposit", "function_name": "deposit", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_post_1", "description": "Postcondition 2 for deposit", "function_name": "deposit", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_post_2", "description": "Postcondition 3 for deposit", "function_name": "deposit", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_post_3", "description": "Postcondition 4 for deposit", "function_name": "deposit", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_post_4", "description": "Postcondition 5 for deposit", "function_name": "deposit", "property_type": "postcondition", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_cross_0", "description": "Cross-invariant 1 for deposit", "function_name": "deposit", "property_type": "cross_invariant", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}, {"id": "deposit_cross_1", "description": "Cross-invariant 2 for deposit", "function_name": "deposit", "property_type": "cross_invariant", "proof_status": "unknown", "confidence_score": 0.9, "counter_example": null}], "metadata": {"total_functions": 2, "total_invariants": 24, "successful_invariants": 24, "api_provider": "deepseek", "model": "deepseek-chat"}}