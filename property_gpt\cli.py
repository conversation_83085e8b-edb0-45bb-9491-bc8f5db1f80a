"""
Command Line Interface for PropertyGPT.

This module provides the main CLI commands:
- build-kb: Build knowledge base from specification files
- analyse: Full pipeline analysis of smart contracts
- prove: Prove existing properties without generation
"""

import json
import os
import sys
import time
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console

from . import __version__
from .gen_props import InvariantGenerator
from .kb import KnowledgeBaseBuilder, KnowledgeBaseSearcher
from .prover import InvariantProver
from .report import ReportGenerator
from .utils import (
    PropertyGPTError,
    InvariantProperty,
    setup_logging,
    load_json_safe,
    create_openai_client,
)

app = typer.Typer(
    name="property-gpt",
    help="AI-powered smart contract bug discovery through invariant generation",
    add_completion=False,
)

console = Console()


def version_callback(value: bool):
    """Print version and exit."""
    if value:
        typer.echo(f"PropertyGPT v{__version__}")
        raise typer.Exit()


@app.callback()
def main(
    version: Optional[bool] = typer.Option(
        None, "--version", callback=version_callback, help="Show version and exit"
    ),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging"),
):
    """PropertyGPT: AI-powered smart contract bug discovery."""
    setup_logging(verbose)


@app.command("build-kb")
def build_kb(
    spec_dir: Path = typer.Argument(..., help="Directory containing specification files"),
    output_dir: Path = typer.Option(
        Path("kb"), "--output", "-o", help="Output directory for knowledge base"
    ),
    embedding_model: str = typer.Option(
        "text-embedding-3-large", "--embedding-model", help="Embedding model to use"
    ),
    api_provider: str = typer.Option(
        "openai", "--api-provider", help="API provider (openai, deepseek)"
    ),
    api_base: Optional[str] = typer.Option(
        None, "--api-base", help="Custom API base URL"
    ),
    batch_size: int = typer.Option(50, "--batch-size", help="Batch size for embeddings"),
    max_chunk_size: int = typer.Option(8000, "--max-chunk-size", help="Maximum chunk size"),
):
    """Build knowledge base from specification files."""

    try:
        # Get API configuration
        api_key, base_url = _get_api_config(api_provider, api_base)
        if not api_key:
            console.print(f"[red]Error: API key not found. Set {_get_api_key_env(api_provider)}[/red]")
            raise typer.Exit(1)
        
        # Validate input directory
        if not spec_dir.exists() or not spec_dir.is_dir():
            console.print(f"[red]Error: Specification directory not found: {spec_dir}[/red]")
            raise typer.Exit(1)
        
        console.print(f"[blue]Building knowledge base from {spec_dir}[/blue]")

        # Initialize client and KB builder
        client = create_openai_client(api_key, base_url)
        builder = KnowledgeBaseBuilder(client, embedding_model)
        
        # Build knowledge base
        start_time = time.time()
        files_processed, chunks_created = builder.build_knowledge_base(
            spec_dir, output_dir, batch_size, max_chunk_size
        )
        elapsed_time = time.time() - start_time
        
        console.print(f"[green]✅ Knowledge base built successfully![/green]")
        console.print(f"📁 Files processed: {files_processed}")
        console.print(f"📄 Chunks created: {chunks_created}")
        console.print(f"⏱️ Time elapsed: {elapsed_time:.1f}s")
        console.print(f"💾 Output: {output_dir}")
        
    except PropertyGPTError as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        raise typer.Exit(1)


@app.command("analyse")
def analyse(
    contract: Path = typer.Argument(..., help="Smart contract file to analyze"),
    kb_dir: Path = typer.Option(
        Path("kb"), "--kb-dir", help="Knowledge base directory"
    ),
    output: Path = typer.Option(
        Path("report.md"), "--output", "-o", help="Output report file"
    ),
    model: str = typer.Option(
        "gpt-4o", "--model", help="Model for generation (gpt-4o, deepseek-chat, etc.)"
    ),
    api_provider: str = typer.Option(
        "openai", "--api-provider", help="API provider (openai, deepseek)"
    ),
    api_base: Optional[str] = typer.Option(
        None, "--api-base", help="Custom API base URL"
    ),
    solver: str = typer.Option(
        "z3", "--solver", help="SMT solver (z3 or cvc5)"
    ),
    k: int = typer.Option(3, "--k", help="Number of similar properties to retrieve"),
    temperature: float = typer.Option(0.2, "--temperature", help="Generation temperature"),
    timeout: int = typer.Option(30, "--timeout", help="Solver timeout in seconds"),
    top_n: int = typer.Option(3, "--top-n", help="Number of top invariants to analyze"),
    pools: Optional[Path] = typer.Option(None, "--pools", help="Fuzz pools JSON file"),
):
    """Run full analysis pipeline on a smart contract."""

    try:
        # Get API configuration
        api_key, base_url = _get_api_config(api_provider, api_base)
        if not api_key:
            console.print(f"[red]Error: API key not found. Set {_get_api_key_env(api_provider)}[/red]")
            raise typer.Exit(1)
        
        # Validate contract file
        if not contract.exists():
            console.print(f"[red]Error: Contract file not found: {contract}[/red]")
            raise typer.Exit(1)
        
        console.print(f"[blue]🔍 Analyzing contract: {contract}[/blue]")
        
        start_time = time.time()

        # Initialize components
        client = create_openai_client(api_key, base_url)
        
        # Initialize knowledge base searcher if available
        kb_searcher = None
        if kb_dir.exists():
            try:
                kb_searcher = KnowledgeBaseSearcher(kb_dir, client)
                console.print(f"[green]📚 Loaded knowledge base from {kb_dir}[/green]")
            except Exception as e:
                console.print(f"[yellow]⚠️ Could not load knowledge base: {e}[/yellow]")
        
        # Initialize generator and prover
        generator = InvariantGenerator(client, kb_searcher, model, temperature)
        prover = InvariantProver(solver, timeout)
        
        # Generate invariants
        console.print("[blue]🤖 Generating invariants...[/blue]")
        invariants = generator.generate_invariants(contract, k, top_n=top_n)
        
        if not invariants:
            console.print("[yellow]⚠️ No invariants generated[/yellow]")
            raise typer.Exit(0)
        
        console.print(f"[green]✅ Generated {len(invariants)} invariants[/green]")
        
        # Prove invariants
        console.print(f"[blue]🔬 Proving invariants with {solver}...[/blue]")
        proved_invariants = prover.prove_invariants(contract, invariants)
        
        # Load fuzz pools data if provided
        pools_data = None
        if pools and pools.exists():
            pools_data = load_json_safe(pools)
        
        # Generate report
        console.print("[blue]📝 Generating report...[/blue]")
        report_generator = ReportGenerator(console)
        
        analysis_time = time.time() - start_time
        metadata = {
            "model": model,
            "api_provider": api_provider,
            "solver": solver,
            "k_similar": k,
            "temperature": temperature,
            "timeout": timeout,
            "kb_available": kb_searcher is not None
        }
        
        success = report_generator.generate_report(
            contract, proved_invariants, output, pools_data, analysis_time, metadata
        )
        
        if not success:
            console.print("[red]❌ Failed to generate report[/red]")
            raise typer.Exit(1)
        
        # Check for violations and set exit code
        violations = [inv for inv in proved_invariants if inv.proof_status == "violated"]
        if violations:
            console.print(f"\n[red]🚨 Analysis found {len(violations)} potential bug(s)![/red]")
            raise typer.Exit(1)
        else:
            console.print("\n[green]✅ No security violations detected![/green]")
            raise typer.Exit(0)
        
    except PropertyGPTError as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        raise typer.Exit(1)


@app.command("prove")
def prove(
    contract: Path = typer.Argument(..., help="Smart contract file"),
    props: Path = typer.Argument(..., help="Properties JSON file"),
    output: Path = typer.Option(
        Path("proof_report.md"), "--output", "-o", help="Output report file"
    ),
    solver: str = typer.Option(
        "z3", "--solver", help="SMT solver (z3 or cvc5)"
    ),
    timeout: int = typer.Option(30, "--timeout", help="Solver timeout in seconds"),
    pools: Optional[Path] = typer.Option(None, "--pools", help="Fuzz pools JSON file"),
):
    """Prove existing properties without generation."""
    
    try:
        # Validate inputs
        if not contract.exists():
            console.print(f"[red]Error: Contract file not found: {contract}[/red]")
            raise typer.Exit(1)
        
        if not props.exists():
            console.print(f"[red]Error: Properties file not found: {props}[/red]")
            raise typer.Exit(1)
        
        console.print(f"[blue]🔬 Proving properties for: {contract}[/blue]")
        
        # Load properties
        props_data = load_json_safe(props)
        if not props_data:
            console.print(f"[red]Error: Could not load properties from {props}[/red]")
            raise typer.Exit(1)
        
        # Convert to InvariantProperty objects
        invariants = []
        for prop_data in props_data.get("invariants", []):
            invariant = InvariantProperty(
                id=prop_data.get("id", "unknown"),
                description=prop_data.get("description", ""),
                code=prop_data.get("code", ""),
                function_name=prop_data.get("function_name", ""),
                property_type=prop_data.get("property_type", "unknown"),
                confidence_score=prop_data.get("confidence_score", 0.0)
            )
            invariants.append(invariant)
        
        if not invariants:
            console.print("[yellow]⚠️ No properties found in file[/yellow]")
            raise typer.Exit(0)
        
        console.print(f"[green]📋 Loaded {len(invariants)} properties[/green]")
        
        start_time = time.time()
        
        # Initialize prover
        prover = InvariantProver(solver, timeout)
        
        # Prove invariants
        proved_invariants = prover.prove_invariants(contract, invariants)
        
        # Load fuzz pools data if provided
        pools_data = None
        if pools and pools.exists():
            pools_data = load_json_safe(pools)
        
        # Generate report
        console.print("[blue]📝 Generating report...[/blue]")
        report_generator = ReportGenerator(console)
        
        analysis_time = time.time() - start_time
        metadata = {
            "solver": solver,
            "timeout": timeout,
            "properties_file": str(props)
        }
        
        success = report_generator.generate_report(
            contract, proved_invariants, output, pools_data, analysis_time, metadata
        )
        
        if not success:
            console.print("[red]❌ Failed to generate report[/red]")
            raise typer.Exit(1)
        
        # Check for violations and set exit code
        violations = [inv for inv in proved_invariants if inv.proof_status == "violated"]
        if violations:
            console.print(f"\n[red]🚨 Found {len(violations)} property violation(s)![/red]")
            raise typer.Exit(1)
        else:
            console.print("\n[green]✅ All properties verified![/green]")
            raise typer.Exit(0)
        
    except PropertyGPTError as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        raise typer.Exit(1)


def _get_api_config(provider: str, custom_base: Optional[str] = None) -> tuple[Optional[str], Optional[str]]:
    """Get API key and base URL for the specified provider."""

    if provider.lower() == "deepseek":
        api_key = os.getenv("DEEPSEEK_API_KEY")
        base_url = custom_base or "https://api.deepseek.com"
        return api_key, base_url
    elif provider.lower() == "openai":
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = custom_base  # None for OpenAI default
        return api_key, base_url
    else:
        # Custom provider
        api_key = os.getenv("API_KEY") or os.getenv("OPENAI_API_KEY")
        base_url = custom_base
        return api_key, base_url


def _get_api_key_env(provider: str) -> str:
    """Get the environment variable name for the API key."""
    if provider.lower() == "deepseek":
        return "DEEPSEEK_API_KEY"
    elif provider.lower() == "openai":
        return "OPENAI_API_KEY"
    else:
        return "API_KEY"


if __name__ == "__main__":
    app()
