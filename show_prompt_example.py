#!/usr/bin/env python3
"""
Show the exact prompt that gets sent to DeepSeek for invariant generation.
"""

from pathlib import Path
from property_gpt.utils import ContractFunction
from property_gpt.gen_props import InvariantGenerator

def show_prompt_example():
    """Show the exact prompt for a specific function."""
    
    print("🔍 PropertyGPT Prompt Example")
    print("=" * 60)
    
    # Create a generator (no client needed for this demo)
    generator = InvariantGenerator(None, None, "deepseek-chat", 0.2)
    
    # Set the contract path for context extraction
    generator._current_contract_path = Path("demo/Bank.sol")
    
    # Create example function (the vulnerable withdraw function)
    withdraw_function = ContractFunction(
        name="withdraw",
        signature="withdraw(uint256)",
        visibility="external",
        state_mutability="nonpayable",
        parameters=[{"name": "amount", "type": "uint256"}],
        returns=[]
    )
    
    print("📋 Function Being Analyzed:")
    print(f"  Name: {withdraw_function.name}")
    print(f"  Signature: {withdraw_function.signature}")
    print(f"  Visibility: {withdraw_function.visibility}")
    print(f"  State Mutability: {withdraw_function.state_mutability}")
    
    print("\n" + "=" * 60)
    print("📝 EXACT PROMPT SENT TO DEEPSEEK:")
    print("=" * 60)
    
    # Generate the exact prompt
    prompt = generator._create_generation_prompt(withdraw_function, [])
    
    print(prompt)
    
    print("=" * 60)
    print("🎯 This is the exact prompt that gets sent to DeepSeek API!")
    print("   DeepSeek then responds with invariants in the specified format.")
    
    return True

if __name__ == "__main__":
    show_prompt_example()
