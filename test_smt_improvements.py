#!/usr/bin/env python3
"""
B-6: Regression tests for SMT translation improvements.
Test each feature: mapping operations, OLD() function, overflow detection, etc.
"""

from pathlib import Path
from property_gpt.utils import InvariantProperty
from property_gpt.prover import InvariantProver

def test_mapping_operations():
    """Test B-2: Mapping & Array Translation."""
    print("🧪 Testing B-2: Mapping Operations...")
    
    prover = InvariantProver("z3", 30)
    
    # Test mapping read operations
    test_invariants = [
        InvariantProperty(
            id="mapping_read_test",
            property_type="postcondition",
            function_name="withdraw",
            code="assert(balances[msg.sender] >= 0);",
            description="Balance non-negative after mapping read",
            confidence_score=0.9
        ),
        InvariantProperty(
            id="mapping_comparison_test", 
            property_type="precondition",
            function_name="transfer",
            code="require(balances[msg.sender] >= amount, \"Insufficient balance\");",
            description="Sufficient balance check with mapping",
            confidence_score=0.9
        )
    ]
    
    contract_path = Path("demo/Bank.sol")
    
    for invariant in test_invariants:
        print(f"\n--- Testing: {invariant.id} ---")
        print(f"Code: {invariant.code}")
        
        smt_content = prover._translate_to_smt(contract_path, invariant)
        
        if smt_content:
            print("✅ SMT translation successful")
            # Check if mapping operations are properly translated
            if "(select balances" in smt_content:
                print("✅ Mapping read operation correctly translated to (select ...)")
            else:
                print("❌ Mapping operation not found in SMT")
            
            print("📄 SMT excerpt:")
            lines = smt_content.split('\n')
            for line in lines:
                if 'select' in line or 'balances' in line:
                    print(f"  {line}")
        else:
            print("❌ SMT translation failed")
    
    return True

def test_old_function_support():
    """Test B-3: OLD() Function Support."""
    print("\n🧪 Testing B-3: OLD() Function Support...")
    
    prover = InvariantProver("z3", 30)
    
    test_invariants = [
        InvariantProperty(
            id="old_simple_test",
            property_type="postcondition",
            function_name="setOwner",
            code="assert(totalSupply == old(totalSupply));",
            description="Total supply unchanged",
            confidence_score=0.9
        ),
        InvariantProperty(
            id="old_mapping_test",
            property_type="postcondition", 
            function_name="deposit",
            code="assert(balances[msg.sender] == old(balances[msg.sender]) + amount);",
            description="Balance increased by deposit amount",
            confidence_score=0.9
        )
    ]
    
    contract_path = Path("demo/Bank.sol")
    
    for invariant in test_invariants:
        print(f"\n--- Testing: {invariant.id} ---")
        print(f"Code: {invariant.code}")
        
        smt_content = prover._translate_to_smt(contract_path, invariant)
        
        if smt_content:
            print("✅ SMT translation successful")
            # Check if OLD() is properly translated to _pre variables
            if "_pre" in smt_content:
                print("✅ OLD() function correctly translated to _pre variables")
            else:
                print("❌ OLD() function translation not found")
            
            print("📄 SMT excerpt:")
            lines = smt_content.split('\n')
            for line in lines:
                if '_pre' in line or 'old' in line.lower():
                    print(f"  {line}")
        else:
            print("❌ SMT translation failed")
    
    return True

def test_bitvec_arithmetic():
    """Test B-4: BitVec Arithmetic Operations."""
    print("\n🧪 Testing B-4: BitVec Arithmetic...")
    
    prover = InvariantProver("z3", 30)
    
    test_invariants = [
        InvariantProperty(
            id="bitvec_addition_test",
            property_type="postcondition",
            function_name="deposit",
            code="assert(totalSupply >= old(totalSupply) + amount);",
            description="Total supply increases correctly",
            confidence_score=0.9
        ),
        InvariantProperty(
            id="bitvec_comparison_test",
            property_type="precondition",
            function_name="withdraw", 
            code="require(amount > 0, \"Amount must be positive\");",
            description="Positive amount check",
            confidence_score=0.9
        )
    ]
    
    contract_path = Path("demo/Bank.sol")
    
    for invariant in test_invariants:
        print(f"\n--- Testing: {invariant.id} ---")
        print(f"Code: {invariant.code}")
        
        smt_content = prover._translate_to_smt(contract_path, invariant)
        
        if smt_content:
            print("✅ SMT translation successful")
            # Check for BitVec operations
            bitvec_ops = ['bvadd', 'bvsub', 'bvuge', 'bvugt', 'bvule', 'bvult', '(_ BitVec 256)']
            found_ops = [op for op in bitvec_ops if op in smt_content]
            
            if found_ops:
                print(f"✅ BitVec operations found: {found_ops}")
            else:
                print("❌ No BitVec operations found")
            
            print("📄 SMT excerpt:")
            lines = smt_content.split('\n')
            for line in lines:
                if any(op in line for op in bitvec_ops):
                    print(f"  {line}")
        else:
            print("❌ SMT translation failed")
    
    return True

def test_solidity_native_smtchecker():
    """Test B-1: Solidity Native SMTChecker."""
    print("\n🧪 Testing B-1: Solidity Native SMTChecker...")
    
    prover = InvariantProver("z3", 30)
    
    # Simple invariant that should work with Solidity SMTChecker
    test_invariant = InvariantProperty(
        id="native_smtchecker_test",
        property_type="postcondition",
        function_name="deposit",
        code="assert(msg.value > 0);",
        description="Deposit value is positive",
        confidence_score=0.9
    )
    
    contract_path = Path("demo/Bank.sol")
    
    print(f"--- Testing: {test_invariant.id} ---")
    print(f"Code: {test_invariant.code}")
    
    # Test the native SMTChecker method directly
    smt_content = prover._solidity_native_smt_translation(contract_path, test_invariant)
    
    if smt_content:
        print("✅ Solidity native SMTChecker successful")
        print("📄 SMT content:")
        print(smt_content)
    else:
        print("⚠️ Solidity native SMTChecker failed (fallback to direct translation)")
        
        # Test fallback
        smt_content = prover._direct_smt_translation(contract_path, test_invariant)
        if smt_content:
            print("✅ Fallback direct translation successful")
        else:
            print("❌ Both native and direct translation failed")
    
    return True

def run_all_tests():
    """Run all SMT improvement tests."""
    print("🚀 Running SMT Translation Improvement Tests")
    print("=" * 60)
    
    tests = [
        test_mapping_operations,
        test_old_function_support, 
        test_bitvec_arithmetic,
        test_solidity_native_smtchecker
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All SMT improvement tests passed!")
    else:
        print("⚠️ Some tests failed - check output above")
    
    return all(results)

if __name__ == "__main__":
    run_all_tests()
