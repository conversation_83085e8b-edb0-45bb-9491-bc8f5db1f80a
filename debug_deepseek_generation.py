#!/usr/bin/env python3
"""
Debug DeepSeek generation step by step.
"""

import os
from pathlib import Path
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def debug_deepseek_step_by_step():
    """Debug each step of DeepSeek generation."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a simple test function
        test_function = ContractFunction(
            name="deposit",
            signature="deposit()",
            visibility="external",
            state_mutability="payable",
            parameters=[],
            returns=[]
        )
        
        print("🧠 Step 1: Create generation prompt...")
        
        # Get the prompt that would be sent to DeepSeek
        prompt = generator._create_generation_prompt(test_function, [])
        print(f"✅ Prompt created (length: {len(prompt)} chars)")
        print(f"📝 Prompt preview:")
        print("-" * 50)
        print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
        print("-" * 50)
        
        print(f"\n🤖 Step 2: Call DeepSeek API...")
        
        # Make the API call
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=1000
        )
        
        generated_text = response.choices[0].message.content
        print(f"✅ DeepSeek response received (length: {len(generated_text)} chars)")
        print(f"📝 DeepSeek response:")
        print("-" * 50)
        print(generated_text)
        print("-" * 50)
        
        print(f"\n🔍 Step 3: Parse generated invariants...")
        
        # Parse the response
        invariants = generator._parse_generated_invariants(generated_text, test_function)
        print(f"✅ Parsed {len(invariants)} invariants")
        
        if invariants:
            for i, inv in enumerate(invariants):
                print(f"\n--- Invariant {i+1} ---")
                print(f"ID: {inv.id}")
                print(f"Type: {inv.property_type}")
                print(f"Code: {inv.code}")
                print(f"Description: {inv.description}")
        else:
            print("❌ No invariants were parsed from the response")
            print("🔍 Let's check what went wrong in parsing...")
            
            # Debug the parsing
            lines = generated_text.strip().split('\n')
            print(f"📊 Response has {len(lines)} lines")
            
            for i, line in enumerate(lines[:10]):  # Show first 10 lines
                print(f"Line {i+1}: {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Debug DeepSeek Generation Step by Step")
    print("=" * 60)
    
    success = debug_deepseek_step_by_step()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 Debug completed - check the output above")
    else:
        print("❌ Debug failed")
