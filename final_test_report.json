{"contract_path": "demo\\Bank.sol", "analysis_timestamp": "2025-07-10T13:46:03.486210", "analysis_time_seconds": 222.**************, "total_invariants": 3, "results": {"violated": 0, "verified": 3, "timeout": 0, "unknown": 0}, "invariants": [{"id": "setOwner_post_0", "description": "Postcondition 1 for setOwner", "function_name": "<PERSON><PERSON><PERSON><PERSON>", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.234, "counter_example": null}, {"id": "getBalance_post_4", "description": "Postcondition 5 for getBalance", "function_name": "getBalance", "property_type": "postcondition", "proof_status": "verified", "confidence_score": 0.228, "counter_example": null}, {"id": "deposit_cross_0", "description": "Cross-invariant 1 for deposit", "function_name": "deposit", "property_type": "cross_invariant", "proof_status": "verified", "confidence_score": 0.*****************, "counter_example": null}], "metadata": {"model": "deepseek-chat", "api_provider": "deepseek", "solver": "z3", "k_similar": 3, "temperature": 0.2, "timeout": 30, "kb_available": false}}