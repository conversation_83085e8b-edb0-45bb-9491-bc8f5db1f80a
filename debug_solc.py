#!/usr/bin/env python3
"""
Debug script to test Solidity compilation.
"""

import tempfile
from pathlib import Path
from property_gpt.utils import run_command, create_temp_file

def test_solc_compilation():
    """Test if solc is working and can compile basic contracts."""
    
    print("🔧 Testing Solidity compiler...")
    
    # Test 1: Check if solc is available
    cmd = ["solc", "--version"]
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode != 0:
        print("❌ solc not found or not working")
        print(f"Error: {stderr}")
        return False
    
    print(f"✅ solc found: {stdout.strip()}")
    
    # Test 2: Try to compile a simple contract
    simple_contract = """
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Test {
    uint256 public value;
    
    function setValue(uint256 _value) public {
        require(_value > 0, "Value must be positive");
        value = _value;
        assert(value == _value);
    }
}
"""
    
    print("\n🧪 Testing basic contract compilation...")
    temp_file = create_temp_file(simple_contract)
    
    try:
        cmd = ["solc", str(temp_file)]
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode == 0:
            print("✅ Basic contract compiles successfully")
        else:
            print(f"❌ Basic contract compilation failed: {stderr}")
            return False
    finally:
        temp_file.unlink(missing_ok=True)
    
    # Test 3: Try formal verification flag
    print("\n🔍 Testing formal verification flag...")
    temp_file = create_temp_file(simple_contract)
    
    try:
        cmd = ["solc", "--formal-verification", str(temp_file)]
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode == 0:
            print("✅ Formal verification flag works")
        else:
            print(f"⚠️ Formal verification flag failed: {stderr}")
            print("This might be expected - not all solc versions support this flag")
    finally:
        temp_file.unlink(missing_ok=True)
    
    # Test 4: Try compiling with Bank.sol
    print("\n🏦 Testing Bank.sol compilation...")
    cmd = ["solc", "demo/Bank.sol"]
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        print("✅ Bank.sol compiles successfully")
    else:
        print(f"❌ Bank.sol compilation failed: {stderr}")
        return False
    
    return True

def test_invariant_compilation():
    """Test compiling a contract with invariants."""
    
    print("\n🧪 Testing invariant compilation...")
    
    # Create a test contract with a simple invariant
    test_contract = """
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Bank {
    mapping(address => uint256) public balances;
    uint256 public totalSupply;
    
    function withdraw(uint256 amount) external {
        balances[msg.sender] -= amount;
        totalSupply -= amount;
    }
}

contract TestInvariant {
    function testInvariant() public pure {
        require(true, "This should always pass");
    }
}
"""
    
    temp_file = create_temp_file(test_contract)
    
    try:
        cmd = ["solc", str(temp_file)]
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode == 0:
            print("✅ Contract with invariant compiles successfully")
            return True
        else:
            print(f"❌ Contract with invariant failed: {stderr}")
            return False
    finally:
        temp_file.unlink(missing_ok=True)

if __name__ == "__main__":
    print("🚀 Testing Solidity Compilation Environment")
    print("=" * 50)
    
    basic_ok = test_solc_compilation()
    if basic_ok:
        invariant_ok = test_invariant_compilation()
    else:
        invariant_ok = False
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print(f"  Basic compilation: {'✅ PASS' if basic_ok else '❌ FAIL'}")
    print(f"  Invariant compilation: {'✅ PASS' if invariant_ok else '❌ FAIL'}")
    
    if basic_ok and invariant_ok:
        print("\n🎉 Solidity compilation environment is working!")
    else:
        print("\n⚠️ Solidity compilation issues detected.")
