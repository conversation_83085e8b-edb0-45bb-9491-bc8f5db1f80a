#!/usr/bin/env python3
"""
Debug script to test Slither function parsing.
"""

import json
from property_gpt.gen_props import InvariantGenerator
from property_gpt.utils import run_command

def test_slither_parsing():
    """Test Slither function parsing."""
    
    print("🔍 Testing Slither function parsing...")
    
    # Test direct Slither command
    cmd = ["slither", "demo/Bank.sol", "--print", "function-summary", "--json", "-"]
    returncode, stdout, stderr = run_command(cmd)
    
    print(f"Return code: {returncode}")
    print(f"Stderr: {stderr[:500]}...")
    
    if returncode == 0:
        try:
            slither_output = json.loads(stdout)
            print("✅ Slither JSON parsed successfully")
            
            # Test our function extraction
            generator = InvariantGenerator(None)  # No client needed for parsing
            functions = generator._extract_functions_from_slither(slither_output)
            
            print(f"📊 Extracted {len(functions)} functions:")
            for func in functions:
                print(f"  - {func.name} ({func.visibility})")
            
            if not functions:
                print("❌ No functions extracted - debugging...")
                
                # Debug the structure
                print("\n🔍 Slither output structure:")
                print(f"Keys: {list(slither_output.keys())}")
                
                results = slither_output.get("results", {})
                print(f"Results keys: {list(results.keys())}")
                
                printers = results.get("printers", [])
                print(f"Number of printers: {len(printers)}")
                
                if printers:
                    printer = printers[0]
                    print(f"First printer keys: {list(printer.keys())}")
                    print(f"Printer type: {printer.get('printer')}")
                    
                    description = printer.get("description", "")
                    print(f"Description length: {len(description)}")
                    print(f"Description preview: {description[:200]}...")
                    
                    # Test our text parsing
                    parsed_functions = generator._parse_function_summary_text(description)
                    print(f"Parsed {len(parsed_functions)} functions from text")
                    for func in parsed_functions:
                        print(f"  - {func.name} ({func.visibility})")
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Raw output: {stdout[:500]}...")
    else:
        print(f"❌ Slither failed: {stderr}")
        
        # Test fallback parsing
        print("\n🔄 Testing fallback parsing...")
        generator = InvariantGenerator(None)
        functions = generator._fallback_parse_functions("demo/Bank.sol")
        print(f"📊 Fallback extracted {len(functions)} functions:")
        for func in functions:
            print(f"  - {func.name} ({func.visibility})")

if __name__ == "__main__":
    test_slither_parsing()
