"""
Knowledge Base Builder for PropertyGPT.

This module builds a knowledge base from Certora/Slither specification files by:
1. Ingesting spec files from a directory
2. Extracting meaningful content and functions
3. Creating embeddings using OpenAI's text-embedding-3-large
4. Storing embeddings in FAISS index with JSONL metadata
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import faiss
import numpy as np
from openai import OpenAI
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from .utils import (
    PropertyGPTError,
    find_spec_files,
    read_file_safe,
    extract_functions_from_spec,
    ensure_directory,
    save_json_safe,
    load_json_safe,
)


class KnowledgeBaseBuilder:
    """Builds and manages the PropertyGPT knowledge base."""

    def __init__(self, openai_client: OpenAI, embedding_model: str = "text-embedding-3-large"):
        """
        Initialize the knowledge base builder.

        Args:
            openai_client: OpenAI-compatible client instance
            embedding_model: Embedding model to use
        """
        self.client = openai_client
        self.embedding_model = embedding_model
        self.embedding_dim = self._get_embedding_dimension(embedding_model)
        
    def build_knowledge_base(
        self,
        spec_dir: Path,
        output_dir: Path,
        batch_size: int = 50,
        max_chunk_size: int = 8000
    ) -> Tuple[int, int]:
        """
        Build knowledge base from specification files.
        
        Args:
            spec_dir: Directory containing spec files
            output_dir: Output directory for KB files
            batch_size: Batch size for embedding requests
            max_chunk_size: Maximum characters per text chunk
            
        Returns:
            Tuple of (total_files_processed, total_chunks_created)
        """
        logging.info(f"Building knowledge base from {spec_dir}")
        
        # Find all spec files
        spec_files = find_spec_files(spec_dir)
        if not spec_files:
            raise PropertyGPTError(f"No specification files found in {spec_dir}")
        
        logging.info(f"Found {len(spec_files)} specification files")
        
        # Process files and extract chunks
        chunks = []
        metadata = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
        ) as progress:
            
            task = progress.add_task("Processing spec files...", total=len(spec_files))
            
            for spec_file in spec_files:
                file_chunks, file_metadata = self._process_spec_file(spec_file, max_chunk_size)
                chunks.extend(file_chunks)
                metadata.extend(file_metadata)
                progress.advance(task)
        
        if not chunks:
            raise PropertyGPTError("No valid content extracted from specification files")
        
        logging.info(f"Extracted {len(chunks)} text chunks")
        
        # Create embeddings
        embeddings = self._create_embeddings(chunks, batch_size)
        
        # Build FAISS index
        index = self._build_faiss_index(embeddings)
        
        # Save knowledge base
        ensure_directory(output_dir)
        self._save_knowledge_base(index, metadata, output_dir)
        
        logging.info(f"Knowledge base saved to {output_dir}")
        return len(spec_files), len(chunks)
    
    def _process_spec_file(self, spec_file: Path, max_chunk_size: int) -> Tuple[List[str], List[Dict]]:
        """
        Process a single specification file.
        
        Args:
            spec_file: Path to spec file
            max_chunk_size: Maximum chunk size
            
        Returns:
            Tuple of (text_chunks, metadata_list)
        """
        content = read_file_safe(spec_file)
        if not content:
            return [], []
        
        # Extract functions and rules
        functions = extract_functions_from_spec(content)
        
        chunks = []
        metadata = []
        
        # Create chunks from the full content
        content_chunks = self._split_text(content, max_chunk_size)
        for i, chunk in enumerate(content_chunks):
            if len(chunk.strip()) < 50:  # Skip very short chunks
                continue
                
            chunks.append(chunk)
            metadata.append({
                'source_file': str(spec_file),
                'chunk_id': i,
                'chunk_type': 'content',
                'functions_count': len(functions),
                'file_size': len(content)
            })
        
        # Create chunks from individual functions/rules
        for j, func in enumerate(functions):
            if len(func.strip()) < 20:  # Skip very short functions
                continue
                
            chunks.append(func)
            metadata.append({
                'source_file': str(spec_file),
                'chunk_id': f"func_{j}",
                'chunk_type': 'function',
                'function_index': j,
                'file_size': len(content)
            })
        
        return chunks, metadata
    
    def _split_text(self, text: str, max_size: int) -> List[str]:
        """
        Split text into chunks of maximum size.
        
        Args:
            text: Text to split
            max_size: Maximum chunk size
            
        Returns:
            List of text chunks
        """
        if len(text) <= max_size:
            return [text]
        
        chunks = []
        lines = text.split('\n')
        current_chunk = []
        current_size = 0
        
        for line in lines:
            line_size = len(line) + 1  # +1 for newline
            
            if current_size + line_size > max_size and current_chunk:
                # Save current chunk and start new one
                chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
                current_size = line_size
            else:
                current_chunk.append(line)
                current_size += line_size
        
        # Add final chunk
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return chunks
    
    def _create_embeddings(self, texts: List[str], batch_size: int) -> np.ndarray:
        """
        Create embeddings for text chunks.
        
        Args:
            texts: List of text chunks
            batch_size: Batch size for API requests
            
        Returns:
            NumPy array of embeddings
        """
        embeddings = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
        ) as progress:
            
            task = progress.add_task("Creating embeddings...", total=len(texts))
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                try:
                    response = self.client.embeddings.create(
                        model=self.embedding_model,
                        input=batch
                    )
                    
                    batch_embeddings = [data.embedding for data in response.data]
                    embeddings.extend(batch_embeddings)
                    
                except Exception as e:
                    logging.error(f"Failed to create embeddings for batch {i//batch_size + 1}: {e}")
                    # Create zero embeddings as fallback
                    batch_embeddings = [[0.0] * self.embedding_dim] * len(batch)
                    embeddings.extend(batch_embeddings)
                
                progress.advance(task, len(batch))
        
        return np.array(embeddings, dtype=np.float32)

    def _get_embedding_dimension(self, model: str) -> int:
        """Get embedding dimension for the specified model."""
        # Common embedding dimensions
        model_dims = {
            "text-embedding-3-large": 3072,
            "text-embedding-3-small": 1536,
            "text-embedding-ada-002": 1536,
            # DeepSeek models (assuming similar to OpenAI)
            "deepseek-embedding": 1536,
        }

        return model_dims.get(model, 1536)  # Default to 1536
    
    def _build_faiss_index(self, embeddings: np.ndarray) -> faiss.Index:
        """
        Build FAISS index from embeddings.
        
        Args:
            embeddings: NumPy array of embeddings
            
        Returns:
            FAISS index
        """
        logging.info(f"Building FAISS index with {len(embeddings)} embeddings")
        
        # Use IndexFlatIP for cosine similarity (after normalization)
        index = faiss.IndexFlatIP(self.embedding_dim)
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        
        # Add embeddings to index
        index.add(embeddings)
        
        return index
    
    def _save_knowledge_base(self, index: faiss.Index, metadata: List[Dict], output_dir: Path) -> None:
        """
        Save knowledge base to disk.
        
        Args:
            index: FAISS index
            metadata: List of metadata dictionaries
            output_dir: Output directory
        """
        # Save FAISS index
        index_path = output_dir / "kb.index"
        faiss.write_index(index, str(index_path))
        
        # Save metadata as JSONL
        metadata_path = output_dir / "kb.jsonl"
        with open(metadata_path, 'w') as f:
            for item in metadata:
                f.write(json.dumps(item) + '\n')
        
        # Save configuration
        config = {
            'embedding_model': self.embedding_model,
            'embedding_dim': self.embedding_dim,
            'total_chunks': len(metadata),
            'index_type': 'IndexFlatIP'
        }
        save_json_safe(config, output_dir / "kb_config.json")


class KnowledgeBaseSearcher:
    """Searches the PropertyGPT knowledge base."""
    
    def __init__(self, kb_dir: Path, openai_client: OpenAI):
        """
        Initialize the knowledge base searcher.
        
        Args:
            kb_dir: Directory containing KB files
            openai_client: OpenAI client instance
        """
        self.kb_dir = kb_dir
        self.client = openai_client
        
        # Load configuration
        config = load_json_safe(kb_dir / "kb_config.json")
        if not config:
            raise PropertyGPTError(f"Failed to load KB configuration from {kb_dir}")
        
        self.embedding_model = config['embedding_model']
        self.embedding_dim = config['embedding_dim']
        
        # Load FAISS index
        index_path = kb_dir / "kb.index"
        if not index_path.exists():
            raise PropertyGPTError(f"FAISS index not found at {index_path}")
        
        self.index = faiss.read_index(str(index_path))
        
        # Load metadata
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> List[Dict]:
        """Load metadata from JSONL file."""
        metadata_path = self.kb_dir / "kb.jsonl"
        if not metadata_path.exists():
            raise PropertyGPTError(f"Metadata file not found at {metadata_path}")
        
        metadata = []
        with open(metadata_path, 'r') as f:
            for line in f:
                metadata.append(json.loads(line.strip()))
        
        return metadata
    
    def search(self, query: str, k: int = 5) -> List[Dict]:
        """
        Search for similar properties in the knowledge base.
        
        Args:
            query: Search query
            k: Number of results to return
            
        Returns:
            List of search results with metadata
        """
        # Create embedding for query
        try:
            response = self.client.embeddings.create(
                model=self.embedding_model,
                input=[query]
            )
            query_embedding = np.array([response.data[0].embedding], dtype=np.float32)
        except Exception as e:
            logging.error(f"Failed to create query embedding: {e}")
            return []
        
        # Normalize for cosine similarity
        faiss.normalize_L2(query_embedding)
        
        # Search index
        scores, indices = self.index.search(query_embedding, k)
        
        # Prepare results
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx < len(self.metadata):
                result = self.metadata[idx].copy()
                result['similarity_score'] = float(score)
                results.append(result)
        
        return results
