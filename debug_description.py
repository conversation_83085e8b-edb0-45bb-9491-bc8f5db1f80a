#!/usr/bin/env python3
"""
Debug script to examine Slither description content.
"""

import json
from property_gpt.utils import run_command

def examine_description():
    """Examine the Slither description content."""
    
    cmd = ["slither", "demo/Bank.sol", "--print", "function-summary", "--json", "-"]
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        slither_output = json.loads(stdout)
        description = slither_output["results"]["printers"][0]["description"]
        
        print("📄 Full description content:")
        print("=" * 80)
        print(description)
        print("=" * 80)
        
        print("\n🔍 Line by line analysis:")
        lines = description.split('\n')
        for i, line in enumerate(lines):
            clean_line = line.replace('\x1b[96m', '').replace('\x1b[34m', '').replace('\x1b[0m', '').replace('\x1b[36m', '')
            print(f"{i:2d}: {repr(line)}")
            print(f"    Clean: {repr(clean_line)}")
            if '|' in clean_line and ('Function' in clean_line or any(word in clean_line for word in ['deposit', 'withdraw', 'transfer'])):
                print(f"    *** FUNCTION LINE ***")

if __name__ == "__main__":
    examine_description()
