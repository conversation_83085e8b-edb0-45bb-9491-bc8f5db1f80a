# PropertyGPT Configuration Example
# Copy this file to .env and set your preferred API provider

# Option 1: OpenAI (default, higher cost, excellent performance)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Option 2: DeepSeek (recommended, ~10x cheaper, very good performance)
# DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# Option 3: Custom API provider
# API_KEY=your-custom-api-key
# CUSTOM_API_BASE=https://your-api-endpoint.com/v1

# Default settings (optional)
PROPERTY_GPT_SOLVER=z3
PROPERTY_GPT_MODEL=gpt-4o
PROPERTY_GPT_TEMPERATURE=0.2
PROPERTY_GPT_TIMEOUT=30

# Example usage:
# For OpenAI: property-gpt analyse contract.sol
# For DeepSeek: property-gpt analyse contract.sol --api-provider deepseek --model deepseek-chat
# For Custom: property-gpt analyse contract.sol --api-provider custom --api-base $CUSTOM_API_BASE
