#!/usr/bin/env python3
"""
Debug why Solidity native <PERSON><PERSON><PERSON><PERSON> is failing.
"""

from pathlib import Path
from property_gpt.utils import InvariantProperty, create_temp_file, run_command
from property_gpt.prover import InvariantProver

def debug_solidity_smtchecker():
    """Debug the Solidity SMTChecker step by step."""
    
    print("🔍 Debugging Solidity Native SMTChecker")
    print("=" * 50)
    
    # Create test invariant
    test_invariant = InvariantProperty(
        id="debug_test",
        property_type="postcondition",
        function_name="deposit",
        code="assert(true);",  # Simplest possible assertion
        description="Trivial assertion",
        confidence_score=0.9
    )
    
    contract_path = Path("demo/Bank.sol")
    prover = InvariantProver("z3", 30)
    
    print(f"📋 Test invariant: {test_invariant.code}")
    print(f"📁 Contract: {contract_path}")
    
    # Step 1: Create contract with inlined invariant
    print(f"\n🔧 Step 1: Creating contract with inlined invariant...")
    temp_contract = prover._create_contract_with_inlined_invariant(contract_path, test_invariant)
    
    if not temp_contract:
        print("❌ Failed to create contract with inlined invariant")
        return False
    
    print("✅ Contract with inlined invariant created")
    print(f"📄 Contract length: {len(temp_contract)} characters")
    
    # Save the temp contract for inspection
    with open("debug_inlined_contract.sol", "w") as f:
        f.write(temp_contract)
    print("💾 Saved to debug_inlined_contract.sol")
    
    # Step 2: Test Solidity compilation
    print(f"\n🔧 Step 2: Testing basic Solidity compilation...")
    temp_file = create_temp_file(temp_contract, suffix=".sol")
    
    try:
        # First test basic compilation
        cmd = ["solc", str(temp_file)]
        returncode, stdout, stderr = run_command(cmd, timeout=10)
        
        print(f"📊 Basic compilation result:")
        print(f"   Return code: {returncode}")
        print(f"   Stdout: {stdout}")
        print(f"   Stderr: {stderr}")
        
        if returncode != 0:
            print("❌ Basic compilation failed!")
            return False
        
        print("✅ Basic compilation successful")
        
        # Step 3: Test SMTChecker
        print(f"\n🔧 Step 3: Testing Solidity SMTChecker...")
        cmd = [
            "solc",
            "--model-checker-engine", "chc",  # Use CHC engine (Constrained Horn Clauses)
            "--model-checker-solvers", "z3",  # Use Z3 solver
            "--model-checker-targets", "underflow,overflow,assert",
            "--model-checker-timeout", "5000",  # 5 seconds
            "--model-checker-show-unproved",
            str(temp_file)
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        returncode, stdout, stderr = run_command(cmd, timeout=30)
        
        print(f"📊 SMTChecker result:")
        print(f"   Return code: {returncode}")
        print(f"   Stdout: {stdout}")
        print(f"   Stderr: {stderr}")
        
        # Step 4: Analyze the result
        print(f"\n🔍 Step 4: Analyzing SMTChecker output...")
        
        if returncode == 0 and "Warning" not in stderr:
            print("✅ SMTChecker: No warnings - assertion holds")
            return True
        elif "Assertion violation" in stderr or "CHC: Assertion violation" in stderr:
            print("⚠️ SMTChecker: Found assertion violation (counterexample)")
            return True
        elif "SMTChecker" in stderr and "not available" in stderr:
            print("❌ SMTChecker: Z3 not available or not properly configured")
            return False
        elif "model checker engine" in stderr.lower():
            print("❌ SMTChecker: Model checker engine issue")
            return False
        else:
            print(f"❓ SMTChecker: Inconclusive result")
            print(f"   This might be expected for complex invariants")
            return True
            
    finally:
        temp_file.unlink(missing_ok=True)
    
    return True

def test_solc_smtchecker_availability():
    """Test if solc has SMTChecker support."""
    
    print("\n🔍 Testing solc SMTChecker availability...")
    
    # Test solc version
    cmd = ["solc", "--version"]
    returncode, stdout, stderr = run_command(cmd, timeout=5)
    
    print(f"📊 solc version:")
    print(f"   Return code: {returncode}")
    print(f"   Output: {stdout}")
    
    if returncode != 0:
        print("❌ solc not available")
        return False
    
    # Test SMTChecker support
    cmd = ["solc", "--help"]
    returncode, stdout, stderr = run_command(cmd, timeout=5)
    
    if "model-checker" in stdout:
        print("✅ solc has model-checker support")
    else:
        print("❌ solc does not have model-checker support")
        return False
    
    # Test Z3 availability
    cmd = ["z3", "--version"]
    returncode, stdout, stderr = run_command(cmd, timeout=5)
    
    print(f"📊 Z3 availability:")
    print(f"   Return code: {returncode}")
    print(f"   Output: {stdout}")
    
    if returncode == 0:
        print("✅ Z3 is available")
        return True
    else:
        print("❌ Z3 is not available")
        return False

if __name__ == "__main__":
    print("🚀 Debugging Solidity SMTChecker")
    print("=" * 60)
    
    # First check if tools are available
    tools_available = test_solc_smtchecker_availability()
    
    if not tools_available:
        print("\n❌ Required tools not available - cannot proceed")
        exit(1)
    
    # Then test the actual SMTChecker
    success = debug_solidity_smtchecker()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 Solidity SMTChecker debugging completed")
    else:
        print("❌ Solidity SMTChecker debugging failed")
