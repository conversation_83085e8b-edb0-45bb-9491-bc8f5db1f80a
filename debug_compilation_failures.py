#!/usr/bin/env python3
"""
Debug why all generated invariants are failing compilation.
"""

import os
from pathlib import Path
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def debug_compilation_failures():
    """Debug each compilation failure in detail."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a simple test function
        test_function = ContractFunction(
            name="deposit",
            signature="deposit(uint256)",
            visibility="external",
            state_mutability="payable",
            parameters=[{"name": "amount", "type": "uint256"}],
            returns=[]
        )
        
        print("🧠 Step 1: Generate invariants with improved prompt...")
        
        # Set the contract path for context extraction
        generator._current_contract_path = Path("demo/Bank.sol")
        
        # Get the improved prompt
        prompt = generator._create_generation_prompt(test_function, [])
        print(f"✅ Improved prompt created (length: {len(prompt)} chars)")
        print(f"📝 Prompt preview:")
        print("-" * 60)
        print(prompt)
        print("-" * 60)
        
        print(f"\n🤖 Step 2: Call DeepSeek API...")
        
        # Make the API call
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=1000
        )
        
        generated_text = response.choices[0].message.content
        print(f"✅ DeepSeek response received (length: {len(generated_text)} chars)")
        print(f"📝 DeepSeek response:")
        print("-" * 60)
        print(generated_text)
        print("-" * 60)
        
        print(f"\n🔍 Step 3: Parse generated invariants...")
        
        # Parse the response
        invariants = generator._parse_generated_invariants(generated_text, test_function)
        print(f"✅ Parsed {len(invariants)} invariants")
        
        if not invariants:
            print("❌ No invariants were parsed!")
            return
        
        print(f"\n🔧 Step 4: Test compilation for each invariant...")
        
        for i, inv in enumerate(invariants):
            print(f"\n--- Testing Invariant {i+1} ---")
            print(f"Type: {inv.property_type}")
            print(f"Code: {inv.code}")
            print(f"Description: {inv.description}")
            
            # Test compilation
            print(f"🔧 Compiling...")
            compiled_inv = generator._compile_and_fix_invariant(inv, Path("demo/Bank.sol"), max_retries=1)
            
            if compiled_inv:
                print(f"✅ Compilation status: {compiled_inv.compilation_status}")
                if compiled_inv.compilation_status == "failed":
                    print(f"❌ Compilation error: {compiled_inv.compilation_error}")
                    
                    # Let's also look at the generated test contract
                    test_contract = generator._create_test_contract(inv, Path("demo/Bank.sol"))
                    with open(f"debug_contract_{i+1}.sol", "w") as f:
                        f.write(test_contract)
                    print(f"💾 Saved test contract to debug_contract_{i+1}.sol")
                    
                    # Try manual compilation to see the exact error
                    from property_gpt.utils import run_command
                    cmd = ["solc", f"debug_contract_{i+1}.sol"]
                    returncode, stdout, stderr = run_command(cmd)
                    print(f"🔧 Manual solc result:")
                    print(f"   Return code: {returncode}")
                    print(f"   Stdout: {stdout}")
                    print(f"   Stderr: {stderr}")
                    
                else:
                    print(f"✅ Compiled successfully!")
                    print(f"Final code: {compiled_inv.code}")
            else:
                print("❌ Compilation returned None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Debug Compilation Failures")
    print("=" * 60)
    
    success = debug_compilation_failures()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 Debug completed - check the output above")
    else:
        print("❌ Debug failed")
