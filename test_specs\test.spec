methods {
    balanceOf(address) returns uint256 envfree
    transfer(address, uint256) returns bool
    totalSupply() returns uint256 envfree
}

rule balanceNonNegative(address user) {
    assert balanceOf(user) >= 0;
}

rule transferPreservesTotalSupply(address from, address to, uint256 amount) {
    env e;
    uint256 totalBefore = totalSupply();
    
    transfer(e, to, amount);
    
    uint256 totalAfter = totalSupply();
    assert totalBefore == totalAfter;
}

rule transferIntegrity(address from, address to, uint256 amount) {
    env e;
    require from != to;
    require balanceOf(from) >= amount;
    
    uint256 balanceFromBefore = balanceOf(from);
    uint256 balanceToBefore = balanceOf(to);
    
    transfer(e, to, amount);
    
    uint256 balanceFromAfter = balanceOf(from);
    uint256 balanceToAfter = balanceOf(to);
    
    assert balanceFromAfter == balanceFromBefore - amount;
    assert balanceToAfter == balanceToBefore + amount;
}
