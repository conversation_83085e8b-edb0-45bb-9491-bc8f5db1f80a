Metadata-Version: 2.4
Name: property-gpt
Version: 0.1.0
Summary: CLI application that discovers smart-contract bugs using AI-generated invariants
Author-email: PropertyGPT Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/propertygpt/property-gpt
Project-URL: Repository, https://github.com/propertygpt/property-gpt
Project-URL: Issues, https://github.com/propertygpt/property-gpt/issues
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Security
Classifier: Topic :: Software Development :: Testing
Requires-Python: >=3.11
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: typer>=0.9.0
Requires-Dist: rich>=13.0.0
Requires-Dist: openai>=1.0.0
Requires-Dist: faiss-cpu>=1.7.0
Requires-Dist: slither-analyzer>=0.10.0
Requires-Dist: z3-solver>=4.12.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: requests>=2.28.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-mock>=3.10.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Dynamic: license-file

# PropertyGPT 🤖🔒

**AI-powered smart contract bug discovery through invariant generation**

PropertyGPT automatically discovers security vulnerabilities in smart contracts by generating and proving invariants using GPT-4o, retrieval-augmented generation (RAG), and formal verification.

[![CI](https://github.com/propertygpt/property-gpt/workflows/PropertyGPT%20CI/badge.svg)](https://github.com/propertygpt/property-gpt/actions)
[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 🚀 Quick Start

```bash
# Install PropertyGPT
pip install property-gpt

# Option 1: Use OpenAI (default)
export OPENAI_API_KEY="your-openai-key-here"
property-gpt analyse MyContract.sol

# Option 2: Use DeepSeek (more cost-effective)
export DEEPSEEK_API_KEY="your-deepseek-key-here"
property-gpt analyse MyContract.sol --api-provider deepseek --model deepseek-chat
```

That's it! PropertyGPT will find bugs and generate a comprehensive report in under 30 seconds.

## 💰 API Provider Options

PropertyGPT supports multiple AI providers:

| Provider | Cost | Performance | Setup |
|----------|------|-------------|-------|
| **OpenAI** | Higher | Excellent | `export OPENAI_API_KEY="sk-..."` |
| **DeepSeek** | ~10x cheaper | Very Good | `export DEEPSEEK_API_KEY="sk-..."` |
| **Custom** | Varies | Varies | `export API_KEY="..." --api-base https://...` |

**💡 Recommendation**: Start with DeepSeek for cost-effective analysis, upgrade to OpenAI for maximum accuracy.

## 📦 Installation

### Prerequisites

**System Requirements:**
- Python 3.11 or higher
- macOS or Linux (Windows support via WSL)

**Required Tools:**
```bash
# Install Foundry (for Solidity compilation)
curl -L https://foundry.paradigm.xyz | bash
foundryup

# Install Slither (for contract analysis)
pip install slither-analyzer

# Install Z3 (SMT solver)
# On macOS:
brew install z3
# On Ubuntu/Debian:
sudo apt-get install z3
```

### Install PropertyGPT

```bash
# From PyPI (recommended)
pip install property-gpt

# From source (for development)
git clone https://github.com/propertygpt/property-gpt.git
cd property-gpt
pip install -e .
```

### Configuration

```bash
# Option 1: OpenAI (default)
export OPENAI_API_KEY="sk-your-openai-key-here"

# Option 2: DeepSeek (cost-effective alternative)
export DEEPSEEK_API_KEY="sk-your-deepseek-key-here"

# Option 3: Custom API provider
export API_KEY="your-custom-api-key"

# Optional: Configure default settings
export PROPERTY_GPT_SOLVER="z3"  # or "cvc5"
export PROPERTY_GPT_MODEL="gpt-4o"  # or "deepseek-chat"
```

## 🎯 How It Works (Feynman-Style Explanation)

Imagine you're a detective trying to catch a thief, but instead of a person, you're hunting bugs in smart contracts. Here's how PropertyGPT works:

### 1. **The Knowledge Base** 📚
Think of this as PropertyGPT's "textbook" of known security patterns. We feed it thousands of formal verification specifications from projects like Aave, Compound, and others. It learns what good security properties look like.

### 2. **The Detective Work** 🔍
When you give PropertyGPT a contract:
- It reads the code like a detective examining a crime scene
- It identifies all the important functions (the "suspects")
- For each function, it asks: "What could go wrong here?"

### 3. **The Pattern Matching** 🧩
For each function, PropertyGPT searches its knowledge base: "Have I seen similar code before? What security properties did experts write for similar functions?" This is like a detective saying, "This looks like the same MO as previous cases."

### 4. **The AI Insight** 🤖
Using GPT-4o, PropertyGPT generates new security properties (invariants) by combining:
- Patterns from its knowledge base
- Understanding of the specific contract
- Knowledge of common vulnerability patterns

### 5. **The Proof** ⚖️
Finally, PropertyGPT uses mathematical solvers (Z3/CVC5) to either:
- **Prove** the property is always true (✅ secure)
- **Find a counterexample** showing how to break it (❌ bug found!)

### Example
```solidity
function withdraw(uint256 amount) public {
    balances[msg.sender] -= amount;  // What if amount > balance?
    payable(msg.sender).transfer(amount);
}
```

PropertyGPT generates: `require(balances[msg.sender] >= amount, "Insufficient balance")`
Then proves this can be violated, finding the underflow bug!

## 🛠️ Usage

### Basic Analysis
```bash
# Analyze with OpenAI (default)
property-gpt analyse contracts/MyToken.sol

# Analyze with DeepSeek
property-gpt analyse contracts/MyToken.sol --api-provider deepseek --model deepseek-chat

# Specify output location
property-gpt analyse contracts/MyToken.sol --output reports/analysis.md

# Use different solver
property-gpt analyse contracts/MyToken.sol --solver cvc5
```

### Building Knowledge Base
```bash
# Build KB with OpenAI embeddings
property-gpt build-kb path/to/specs/ --output my_kb/

# Build KB with DeepSeek
property-gpt build-kb path/to/specs/ --api-provider deepseek --output my_kb/

# Use custom KB for analysis
property-gpt analyse MyContract.sol --kb-dir my_kb/
```

### Advanced Options
```bash
# Customize generation parameters with OpenAI
property-gpt analyse MyContract.sol \
  --model gpt-4o-mini \
  --temperature 0.1 \
  --k 5 \
  --timeout 60

# Use DeepSeek with custom parameters
property-gpt analyse MyContract.sol \
  --api-provider deepseek \
  --model deepseek-chat \
  --temperature 0.1

# Use custom API endpoint
property-gpt analyse MyContract.sol \
  --api-provider custom \
  --api-base https://your-api.com/v1 \
  --model your-model

# Prove existing properties
property-gpt prove MyContract.sol properties.jsonl
```

## 📊 Example Output

PropertyGPT generates rich markdown reports:

```markdown
# PropertyGPT Analysis Report

**Contract:** Bank.sol  
**Analysis Time:** 23.4s  
**Total Invariants:** 7

## ❌ Violated Properties

### 1. Balance Underflow Protection
**Function:** `withdraw`  
**Type:** precondition

**Invariant Code:**
```solidity
require(balances[msg.sender] >= amount, "Insufficient balance");
```

**Counterexample:**
- `balances[msg.sender]`: 100
- `amount`: 200
- Result: Integer underflow vulnerability

## ✅ Verified Properties
- Transfer preserves total supply
- Only owner can mint tokens
- Balances are always non-negative
```

## 🏗️ Architecture

PropertyGPT consists of 6 core modules:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Knowledge     │    │    Invariant     │    │     Prover      │
│   Base (kb.py)  │───▶│ Generator        │───▶│   (prover.py)   │
│                 │    │ (gen_props.py)   │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│     Utils       │    │       CLI        │    │     Report      │
│   (utils.py)    │    │    (cli.py)      │    │  (report.py)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

- **Knowledge Base**: Stores and searches formal verification patterns
- **Invariant Generator**: Creates security properties using AI + RAG
- **Prover**: Verifies properties using SMT solvers
- **Report Generator**: Creates comprehensive security reports
- **CLI**: User-friendly command-line interface
- **Utils**: Shared utilities and data structures

## 🧪 Demo

Try the included demo to see PropertyGPT catch a real bug:

```bash
# Run the demo (finds underflow in Bank.sol)
./demo/demo.sh

# Or on Windows
demo\demo.bat
```

The demo analyzes `demo/Bank.sol` which contains an intentional integer underflow vulnerability that PropertyGPT should detect in under 30 seconds.

## 🔬 Development

### Running Tests
```bash
# Install development dependencies
pip install -e ".[dev]"

# Run all tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=property_gpt --cov-report=html
```

### Code Quality
```bash
# Format code
black property_gpt tests

# Type checking
mypy property_gpt

# Security scan
bandit -r property_gpt/
```

## 🚧 Stretch Goals (TODO for v0.2+)

- [ ] **Pairwise Fuzz Integration**: Add `--pools schema.json` support
- [ ] **Pluggable Ranking**: Implement `--ranker llm` and `--ranker heur` options
- [ ] **VS Code Extension**: Inline annotation of violated invariants
- [ ] **CVC5 Support**: Full integration with CVC5 solver
- [ ] **Slither-SMT Integration**: Enhanced SMT translation pipeline
- [ ] **Custom Knowledge Bases**: Domain-specific specification libraries

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
```bash
git clone https://github.com/propertygpt/property-gpt.git
cd property-gpt
pip install -e ".[dev]"
pre-commit install
```

## 📄 License

PropertyGPT is released under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **Certora** for providing formal verification specifications
- **OpenAI** for GPT-4o and embedding models
- **Trail of Bits** for Slither static analysis
- **Microsoft Research** for Z3 SMT solver
- **The Ethereum Foundation** for supporting smart contract security research

## 📚 Citation

If you use PropertyGPT in your research, please cite:

```bibtex
@software{propertygpt2024,
  title={PropertyGPT: AI-Powered Smart Contract Bug Discovery},
  author={PropertyGPT Team},
  year={2024},
  url={https://github.com/propertygpt/property-gpt}
}
```

---

**Built with ❤️ for the Ethereum security community**
