#!/usr/bin/env python3
"""
Debug script to test invariant compilation.
"""

import os
from property_gpt.utils import create_openai_client, ContractFunction
from property_gpt.gen_props import InvariantGenerator

def test_compilation_debug():
    """Test the compilation step in detail."""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not set")
        return
    
    try:
        # Create client and generator
        client = create_openai_client(api_key, "https://api.deepseek.com")
        generator = InvariantGenerator(client, None, "deepseek-chat", 0.2)
        
        # Create a simple test function
        test_function = ContractFunction(
            name="withdraw",
            signature="withdraw(uint256)",
            visibility="external",
            state_mutability="nonpayable",
            parameters=[{"name": "amount", "type": "uint256"}],
            returns=[]
        )
        
        print("🧠 Testing invariant generation and compilation...")
        
        # Generate invariants for this function
        invariants = generator._generate_function_invariants(
            test_function, 
            "demo/Bank.sol", 
            k_similar=0,  # No KB search
            max_retries=1
        )
        
        print(f"📊 Generated {len(invariants)} invariants:")
        for i, inv in enumerate(invariants):
            print(f"  {i+1}. {inv.property_type}: {inv.code}")
            print(f"     Status: {inv.compilation_status}")
            if inv.compilation_status == "failed":
                print(f"     ❌ Compilation failed")
            else:
                print(f"     ✅ Compilation succeeded")
        
        return invariants
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    test_compilation_debug()
