{"source_file": "test_specs\\test.spec", "chunk_id": 0, "chunk_type": "content", "functions_count": 6, "file_size": 964}
{"source_file": "test_specs\\test.spec", "chunk_id": "func_0", "chunk_type": "function", "function_index": 0, "file_size": 964}
{"source_file": "test_specs\\test.spec", "chunk_id": "func_1", "chunk_type": "function", "function_index": 1, "file_size": 964}
{"source_file": "test_specs\\test.spec", "chunk_id": "func_2", "chunk_type": "function", "function_index": 2, "file_size": 964}
{"source_file": "test_specs\\test.spec", "chunk_id": "func_3", "chunk_type": "function", "function_index": 3, "file_size": 964}
{"source_file": "test_specs\\test.spec", "chunk_id": "func_4", "chunk_type": "function", "function_index": 4, "file_size": 964}
{"source_file": "test_specs\\test.spec", "chunk_id": "func_5", "chunk_type": "function", "function_index": 5, "file_size": 964}
